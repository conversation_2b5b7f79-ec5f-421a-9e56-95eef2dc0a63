"""
Vosk语音识别引擎
"""

import os
import json
import asyncio
from typing import Optional, Tu<PERSON>, Dict, Any
from pathlib import Path
import vosk
import wave
import librosa
import soundfile as sf
from app.core.config import settings
from app.core.config_manager import config_manager


class VoskEngine:
    """Vosk语音识别引擎"""
    
    def __init__(self):
        self.model = None
        self.rec = None
        self.model_path = config_manager.get('speech_recognition.vosk_model_path', './models/vosk-model-cn')
        self._is_initialized = False
        
        # 设置Vosk日志级别
        vosk.SetLogLevel(-1)  # 关闭详细日志
    
    async def initialize(self) -> bool:
        """初始化Vosk模型"""
        if self._is_initialized:
            return True
        
        try:
            model_path = Path(self.model_path)
            if not model_path.exists():
                print(f"Vosk模型路径不存在: {model_path}")
                print("请下载Vosk中文模型到指定路径")
                print("下载地址: https://alphacephei.com/vosk/models")
                return False
            
            print(f"正在加载Vosk模型: {model_path}")
            
            # 在线程池中加载模型
            loop = asyncio.get_event_loop()
            self.model = await loop.run_in_executor(
                None,
                vosk.Model,
                str(model_path)
            )
            
            # 创建识别器
            sample_rate = config_manager.get('audio.sample_rate', 16000)
            self.rec = vosk.KaldiRecognizer(self.model, sample_rate)
            
            self._is_initialized = True
            print(f"Vosk模型加载成功: {model_path}")
            return True
            
        except Exception as e:
            print(f"Vosk模型加载失败: {e}")
            return False
    
    async def transcribe(
        self, 
        audio_path: str, 
        language: Optional[str] = "zh"
    ) -> Tuple[str, float, Dict[str, Any]]:
        """
        转录音频文件
        
        Args:
            audio_path: 音频文件路径
            language: 语言代码（Vosk模型已固定语言）
            
        Returns:
            tuple: (转录文本, 置信度, 详细信息)
        """
        if not self._is_initialized:
            if not await self.initialize():
                raise RuntimeError("Vosk模型初始化失败")
        
        if not os.path.exists(audio_path):
            raise FileNotFoundError(f"音频文件不存在: {audio_path}")
        
        try:
            # 预处理音频文件
            processed_audio_path = await self._preprocess_audio(audio_path)
            
            # 在线程池中执行转录
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                None,
                self._transcribe_audio_file,
                processed_audio_path
            )
            
            # 清理临时文件
            if processed_audio_path != audio_path:
                os.unlink(processed_audio_path)
            
            return result
            
        except Exception as e:
            print(f"Vosk转录失败: {e}")
            raise RuntimeError(f"语音转录失败: {str(e)}")
    
    def _transcribe_audio_file(self, audio_path: str) -> Tuple[str, float, Dict[str, Any]]:
        """转录音频文件（同步方法）"""
        try:
            # 打开音频文件
            wf = wave.open(audio_path, 'rb')
            
            # 验证音频格式
            if wf.getnchannels() != 1 or wf.getsampwidth() != 2 or wf.getframerate() != 16000:
                raise ValueError("音频格式不支持，需要16kHz单声道16位WAV格式")
            
            # 重置识别器
            self.rec.Reset()
            
            # 逐块读取和识别
            results = []
            total_confidence = 0.0
            confidence_count = 0
            
            while True:
                data = wf.readframes(4000)  # 读取4000帧
                if len(data) == 0:
                    break
                
                if self.rec.AcceptWaveform(data):
                    result = json.loads(self.rec.Result())
                    if result.get('text'):
                        results.append(result['text'])
                        # 尝试获取置信度
                        if 'confidence' in result:
                            total_confidence += result['confidence']
                            confidence_count += 1
            
            # 获取最终结果
            final_result = json.loads(self.rec.FinalResult())
            if final_result.get('text'):
                results.append(final_result['text'])
                if 'confidence' in final_result:
                    total_confidence += final_result['confidence']
                    confidence_count += 1
            
            wf.close()
            
            # 合并所有文本
            full_text = ' '.join(results).strip()
            
            # 计算平均置信度
            avg_confidence = total_confidence / confidence_count if confidence_count > 0 else 0.7
            
            # 获取音频时长
            duration = self._get_audio_duration(audio_path)
            
            # 详细信息
            details = {
                "language": "zh",
                "duration": duration,
                "segments_count": len(results),
                "model": "Vosk",
                "engine": "Kaldi"
            }
            
            return full_text, avg_confidence, details
            
        except Exception as e:
            raise RuntimeError(f"音频转录处理失败: {str(e)}")
    
    async def _preprocess_audio(self, audio_path: str) -> str:
        """预处理音频文件，转换为Vosk需要的格式"""
        try:
            # 检查文件格式
            if audio_path.lower().endswith('.wav'):
                # 检查是否已经是正确格式
                try:
                    wf = wave.open(audio_path, 'rb')
                    if (wf.getnchannels() == 1 and 
                        wf.getsampwidth() == 2 and 
                        wf.getframerate() == 16000):
                        wf.close()
                        return audio_path  # 格式正确，直接返回
                    wf.close()
                except:
                    pass
            
            # 需要转换格式
            print(f"正在转换音频格式: {audio_path}")
            
            # 使用librosa加载音频
            y, sr = librosa.load(audio_path, sr=16000, mono=True)
            
            # 创建临时文件
            temp_path = audio_path + "_temp_vosk.wav"
            
            # 保存为16kHz单声道WAV
            sf.write(temp_path, y, 16000, subtype='PCM_16')
            
            return temp_path
            
        except Exception as e:
            raise RuntimeError(f"音频预处理失败: {str(e)}")
    
    def _get_audio_duration(self, audio_path: str) -> float:
        """获取音频时长"""
        try:
            y, sr = librosa.load(audio_path)
            duration = librosa.get_duration(y=y, sr=sr)
            return duration
        except Exception:
            return 0.0
    
    async def transcribe_realtime(
        self, 
        audio_data: bytes, 
        is_final: bool = False
    ) -> Tuple[str, bool]:
        """
        实时转录音频数据
        
        Args:
            audio_data: 音频数据字节
            is_final: 是否为最终数据
            
        Returns:
            tuple: (转录文本, 是否为完整句子)
        """
        if not self._is_initialized:
            if not await self.initialize():
                raise RuntimeError("Vosk模型初始化失败")
        
        try:
            if is_final:
                # 最终结果
                result = json.loads(self.rec.FinalResult())
                return result.get('text', ''), True
            else:
                # 中间结果
                if self.rec.AcceptWaveform(audio_data):
                    result = json.loads(self.rec.Result())
                    return result.get('text', ''), True
                else:
                    # 部分结果
                    partial = json.loads(self.rec.PartialResult())
                    return partial.get('partial', ''), False
                    
        except Exception as e:
            print(f"实时转录失败: {e}")
            return "", False
    
    def get_supported_languages(self) -> Dict[str, str]:
        """获取支持的语言列表"""
        return {
            "zh": "中文"
        }
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return {
            "name": "Vosk",
            "model_path": self.model_path,
            "initialized": self._is_initialized,
            "supported_languages": list(self.get_supported_languages().keys()),
            "capabilities": [
                "实时识别",
                "离线工作",
                "低延迟",
                "中文支持"
            ],
            "requirements": [
                "16kHz采样率",
                "单声道",
                "16位深度",
                "WAV格式"
            ]
        }
    
    def cleanup(self):
        """清理资源"""
        if self.rec is not None:
            del self.rec
            self.rec = None
        
        if self.model is not None:
            del self.model
            self.model = None
        
        self._is_initialized = False
        print("Vosk引擎资源已清理")