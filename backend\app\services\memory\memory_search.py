"""
记忆搜索引擎
"""

import re
import json
from typing import Dict, List, Optional, Any
from datetime import datetime
from difflib import SequenceMatcher


class MemorySearchEngine:
    """记忆搜索引擎"""
    
    def __init__(self):
        self.stopwords = {
            "的", "了", "在", "是", "我", "有", "和", "就", "不", "人", "都", "一", "一个",
            "上", "也", "很", "到", "说", "要", "去", "你", "会", "着", "没有", "看", "好",
            "自己", "这", "那", "来", "他", "她", "它", "们", "什么", "怎么", "为什么"
        }
    
    def _extract_keywords(self, text: str) -> List[str]:
        """从文本中提取关键词"""
        if not text:
            return []
        
        # 简单的中文分词（实际项目中建议使用jieba等专业分词库）
        words = re.findall(r'[\u4e00-\u9fff]+|[a-zA-Z]+\d*', text)
        
        # 过滤停用词和短词
        keywords = [
            word.lower() for word in words 
            if len(word) > 1 and word not in self.stopwords
        ]
        
        return list(set(keywords))
    
    def _calculate_similarity(self, text1: str, text2: str) -> float:
        """计算两个文本的相似度"""
        if not text1 or not text2:
            return 0.0
        
        return SequenceMatcher(None, text1.lower(), text2.lower()).ratio()
    
    def _search_in_text(self, text: str, query_keywords: List[str]) -> float:
        """在文本中搜索关键词，返回匹配度"""
        if not text or not query_keywords:
            return 0.0
        
        text_lower = text.lower()
        score = 0.0
        
        for keyword in query_keywords:
            keyword_lower = keyword.lower()
            
            # 完全匹配
            if keyword_lower in text_lower:
                score += 1.0
            else:
                # 模糊匹配
                for word in self._extract_keywords(text):
                    similarity = self._calculate_similarity(keyword_lower, word)
                    if similarity > 0.7:  # 相似度阈值
                        score += similarity * 0.5
        
        # 归一化分数
        return min(score / len(query_keywords), 1.0)
    
    def _search_in_content(self, content: Any, query_keywords: List[str]) -> float:
        """在内容中搜索，支持字典、列表等复杂结构"""
        if isinstance(content, str):
            return self._search_in_text(content, query_keywords)
        
        elif isinstance(content, dict):
            total_score = 0.0
            count = 0
            
            for key, value in content.items():
                # 搜索键名
                key_score = self._search_in_text(str(key), query_keywords)
                if key_score > 0:
                    total_score += key_score
                    count += 1
                
                # 搜索值
                value_score = self._search_in_content(value, query_keywords)
                if value_score > 0:
                    total_score += value_score
                    count += 1
            
            return total_score / count if count > 0 else 0.0
        
        elif isinstance(content, list):
            total_score = 0.0
            count = len(content)
            
            for item in content:
                score = self._search_in_content(item, query_keywords)
                total_score += score
            
            return total_score / count if count > 0 else 0.0
        
        else:
            return self._search_in_text(str(content), query_keywords)
    
    async def search(
        self, 
        memories: Dict[str, Any], 
        query: str, 
        memory_type: Optional[str] = None,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """搜索记忆"""
        query_keywords = self._extract_keywords(query)
        if not query_keywords:
            return []
        
        results = []
        
        # 搜索不同类型的记忆
        categories = []
        if memory_type:
            if memory_type == "conversation":
                categories = ["conversations"]
            elif memory_type == "event":
                categories = ["events"]
            elif memory_type == "reminder":
                categories = ["reminders"]
            elif memory_type == "preference":
                categories = ["preferences"]
            elif memory_type == "habit":
                categories = ["habits"]
        else:
            categories = ["conversations", "events", "reminders", "preferences", "habits"]
        
        for category in categories:
            if category not in memories:
                continue
            
            if category in ["conversations", "events", "reminders"]:
                # 列表类型的记忆
                for memory in memories[category]:
                    score = 0.0
                    
                    # 搜索内容
                    content_score = self._search_in_content(memory.get("content", {}), query_keywords)
                    score += content_score * 0.7
                    
                    # 搜索关键词
                    keywords = memory.get("keywords", [])
                    keyword_score = self._search_in_text(" ".join(keywords), query_keywords)
                    score += keyword_score * 0.3
                    
                    if score > 0:
                        results.append({
                            **memory,
                            "category": category,
                            "search_score": score
                        })
            
            elif category in ["preferences", "habits"]:
                # 字典类型的记忆
                content_score = self._search_in_content(memories[category], query_keywords)
                if content_score > 0:
                    results.append({
                        "id": f"{category}_root",
                        "type": category,
                        "content": memories[category],
                        "keywords": list(memories[category].keys()) if isinstance(memories[category], dict) else [],
                        "created_at": datetime.utcnow().isoformat(),
                        "updated_at": datetime.utcnow().isoformat(),
                        "category": category,
                        "search_score": content_score
                    })
        
        # 按搜索分数排序
        results.sort(key=lambda x: x["search_score"], reverse=True)
        
        # 返回限定数量的结果
        return results[:limit]
    
    async def search_by_keywords(
        self, 
        memories: Dict[str, Any], 
        keywords: List[str],
        memory_type: Optional[str] = None,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """根据关键词搜索记忆"""
        if not keywords:
            return []
        
        results = []
        categories = ["conversations", "events", "reminders"] if not memory_type else [memory_type + "s"]
        
        for category in categories:
            if category not in memories:
                continue
            
            for memory in memories[category]:
                memory_keywords = memory.get("keywords", [])
                
                # 计算关键词匹配度
                matched_keywords = set(keywords) & set(memory_keywords)
                if matched_keywords:
                    score = len(matched_keywords) / len(keywords)
                    results.append({
                        **memory,
                        "category": category,
                        "search_score": score,
                        "matched_keywords": list(matched_keywords)
                    })
        
        # 按匹配度排序
        results.sort(key=lambda x: x["search_score"], reverse=True)
        
        return results[:limit]
    
    async def search_by_date_range(
        self, 
        memories: Dict[str, Any],
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        memory_type: Optional[str] = None,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """根据日期范围搜索记忆"""
        results = []
        categories = ["conversations", "events", "reminders"] if not memory_type else [memory_type + "s"]
        
        for category in categories:
            if category not in memories:
                continue
            
            for memory in memories[category]:
                created_at_str = memory.get("created_at")
                if not created_at_str:
                    continue
                
                try:
                    created_at = datetime.fromisoformat(created_at_str.replace("Z", "+00:00"))
                    
                    # 检查日期范围
                    if start_date and created_at < start_date:
                        continue
                    if end_date and created_at > end_date:
                        continue
                    
                    results.append({
                        **memory,
                        "category": category
                    })
                
                except ValueError:
                    continue
        
        # 按创建时间排序
        results.sort(key=lambda x: x.get("created_at", ""), reverse=True)
        
        return results[:limit]