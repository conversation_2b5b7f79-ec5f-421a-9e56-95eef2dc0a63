import React, { useState, useRef, useEffect } from 'react';
import { Button, Card, Progress, Space, Typography, message, Tooltip } from 'antd';
import { 
  AudioOutlined, 
  PauseCircleOutlined, 
  PlayCircleOutlined,
  StopOutlined,
  SendOutlined,
  DeleteOutlined
} from '@ant-design/icons';

const { Text } = Typography;

interface VoiceRecorderProps {
  onRecordingComplete?: (audioBlob: Blob, duration: number) => void;
  onRecordingStart?: () => void;
  onRecordingStop?: () => void;
  maxDuration?: number; // 最大录制时长（秒）
  disabled?: boolean;
}

const VoiceRecorder: React.FC<VoiceRecorderProps> = ({
  onRecordingComplete,
  onRecordingStart,
  onRecordingStop,
  maxDuration = 60,
  disabled = false
}) => {
  const [isRecording, setIsRecording] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [duration, setDuration] = useState(0);
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);
  const [audioUrl, setAudioUrl] = useState<string>('');
  const [isPlaying, setIsPlaying] = useState(false);

  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, []);

  // 开始录制
  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ 
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          sampleRate: 16000
        } 
      });

      const mediaRecorder = new MediaRecorder(stream, {
        mimeType: 'audio/webm;codecs=opus'
      });

      mediaRecorderRef.current = mediaRecorder;
      audioChunksRef.current = [];

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };

      mediaRecorder.onstop = () => {
        const blob = new Blob(audioChunksRef.current, { type: 'audio/webm' });
        setAudioBlob(blob);
        setAudioUrl(URL.createObjectURL(blob));
        
        // 停止所有音频轨道
        stream.getTracks().forEach(track => track.stop());
      };

      mediaRecorder.start(100); // 每100ms收集一次数据
      setIsRecording(true);
      setIsPaused(false);
      setDuration(0);
      
      // 开始计时
      timerRef.current = setInterval(() => {
        setDuration(prev => {
          const newDuration = prev + 1;
          if (newDuration >= maxDuration) {
            stopRecording();
            return maxDuration;
          }
          return newDuration;
        });
      }, 1000);

      onRecordingStart?.();
      message.success('开始录制语音');

    } catch (error) {
      console.error('录制失败:', error);
      message.error('无法访问麦克风，请检查权限设置');
    }
  };

  // 暂停录制
  const pauseRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      if (isPaused) {
        mediaRecorderRef.current.resume();
        setIsPaused(false);
        
        // 恢复计时
        timerRef.current = setInterval(() => {
          setDuration(prev => {
            const newDuration = prev + 1;
            if (newDuration >= maxDuration) {
              stopRecording();
              return maxDuration;
            }
            return newDuration;
          });
        }, 1000);
        
        message.info('继续录制');
      } else {
        mediaRecorderRef.current.pause();
        setIsPaused(true);
        
        // 暂停计时
        if (timerRef.current) {
          clearInterval(timerRef.current);
        }
        
        message.info('录制已暂停');
      }
    }
  };

  // 停止录制
  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
      setIsPaused(false);
      
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
      
      onRecordingStop?.();
      message.success('录制完成');
    }
  };

  // 播放录音
  const playAudio = () => {
    if (audioUrl && !isPlaying) {
      const audio = new Audio(audioUrl);
      audioRef.current = audio;
      
      audio.onended = () => {
        setIsPlaying(false);
      };
      
      audio.play();
      setIsPlaying(true);
    } else if (audioRef.current && isPlaying) {
      audioRef.current.pause();
      setIsPlaying(false);
    }
  };

  // 删除录音
  const deleteRecording = () => {
    setAudioBlob(null);
    setAudioUrl('');
    setDuration(0);
    setIsPlaying(false);
    
    if (audioRef.current) {
      audioRef.current.pause();
    }
    
    message.info('录音已删除');
  };

  // 发送录音
  const sendRecording = () => {
    if (audioBlob) {
      onRecordingComplete?.(audioBlob, duration);
      message.success('语音已发送');
      deleteRecording(); // 发送后清除录音
    }
  };

  // 格式化时间显示
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // 计算进度百分比
  const progressPercent = (duration / maxDuration) * 100;

  return (
    <Card 
      size="small"
      style={{ width: '100%', maxWidth: 400 }}
      bodyStyle={{ padding: '16px' }}
    >
      <Space direction="vertical" size="middle" style={{ width: '100%' }}>
        {/* 录制状态显示 */}
        <div style={{ display: 'flex', justifyContent: 'space-between', width: '100%' }}>
          <Text strong>
            {isRecording ? (isPaused ? '录制暂停' : '正在录制') : '语音录制'}
          </Text>
          <Text type="secondary">
            {formatTime(duration)} / {formatTime(maxDuration)}
          </Text>
        </div>

        {/* 进度条 */}
        <Progress 
          percent={progressPercent}
          strokeColor={isRecording ? (isPaused ? '#faad14' : '#52c41a') : '#d9d9d9'}
          showInfo={false}
          size="small"
        />

        {/* 录制控制按钮 */}
        {!audioBlob && (
          <div style={{ display: 'flex', justifyContent: 'center', width: '100%' }}>
            {!isRecording ? (
              <Tooltip title="开始录制">
                <Button
                  type="primary"
                  shape="circle"
                  size="large"
                  icon={<AudioOutlined />}
                  onClick={startRecording}
                  disabled={disabled}
                />
              </Tooltip>
            ) : (
              <Space>
                <Tooltip title={isPaused ? "继续录制" : "暂停录制"}>
                  <Button
                    type="default"
                    shape="circle"
                    size="large"
                    icon={isPaused ? <PlayCircleOutlined /> : <PauseCircleOutlined />}
                    onClick={pauseRecording}
                  />
                </Tooltip>

                <Tooltip title="停止录制">
                  <Button
                    type="primary"
                    danger
                    shape="circle"
                    size="large"
                    icon={<StopOutlined />}
                    onClick={stopRecording}
                  />
                </Tooltip>
              </Space>
            )}
          </div>
        )}

        {/* 录音播放和操作 */}
        {audioBlob && (
          <div style={{ display: 'flex', justifyContent: 'center', width: '100%', gap: '8px' }}>
            <Tooltip title={isPlaying ? "暂停播放" : "播放录音"}>
              <Button
                type="default"
                shape="circle"
                icon={isPlaying ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
                onClick={playAudio}
              />
            </Tooltip>

            <Tooltip title="发送录音">
              <Button
                type="primary"
                shape="circle"
                icon={<SendOutlined />}
                onClick={sendRecording}
              />
            </Tooltip>

            <Tooltip title="删除录音">
              <Button
                type="default"
                danger
                shape="circle"
                icon={<DeleteOutlined />}
                onClick={deleteRecording}
              />
            </Tooltip>
          </div>
        )}

        {/* 提示信息 */}
        <Text type="secondary" style={{ textAlign: 'center', fontSize: '12px' }}>
          {isRecording 
            ? '正在录制中，点击暂停或停止按钮结束录制'
            : audioBlob 
              ? '录制完成，可以播放试听或发送'
              : '点击麦克风按钮开始录制语音'
          }
        </Text>
      </Space>
    </Card>
  );
};

export default VoiceRecorder;
