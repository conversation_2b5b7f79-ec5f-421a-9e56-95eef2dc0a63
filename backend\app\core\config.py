"""
配置管理模块
"""

import os
from typing import List
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """应用配置设置"""
    
    # 基础配置
    PROJECT_NAME: str = "J_family00"
    VERSION: str = "0.1.0"
    DEBUG: bool = True
    
    # 服务器配置
    HOST: str = "127.0.0.1"
    PORT: int = 8000
    API_V1_STR: str = "/api/v1"
    
    # 跨域配置
    ALLOWED_HOSTS: List[str] = ["http://localhost:3000", "http://127.0.0.1:3000"]
    
    # 数据库配置
    DATABASE_URL: str = "sqlite:///./data/j_family00.db"
    
    # 文件存储路径
    DATA_DIR: str = "./data"
    MEMORIES_DIR: str = "./data/memories"
    AUDIO_DIR: str = "./data/audio"
    VOICES_DIR: str = "./data/voices"
    MODELS_DIR: str = "./models"
    LOGS_DIR: str = "./logs"
    
    # 语音识别配置
    SPEECH_RECOGNITION_MODEL: str = "sherpa-ncnn"  # sherpa-ncnn
    SHERPA_NCNN_MODEL_PATH: str = "./models/sherpa-ncnn-zh"
    VOSK_MODEL_PATH: str = "./models/vosk-model-cn"
    
    # 声纹识别配置
    VOICE_SIMILARITY_THRESHOLD: float = 0.7  # 70% 匹配度
    VOICE_EMBEDDING_SIZE: int = 256  # WeSpeaker embedding大小
    WESPEAKER_MODEL_PATH: str = "./models/wespeaker/voxceleb_resnet34.onnx"
    
    # 记忆存储配置
    MEMORY_MAX_SIZE: int = 10000  # 单个记忆文件最大条目数
    MEMORY_BACKUP_INTERVAL: int = 24  # 小时

    # 阿里百炼平台配置
    DASHSCOPE_API_KEY: str = "sk-ef249a2ad0fe44bf958c5561a8cae3ed"  # 阿里百炼API密钥
    QWEN_MODEL_NAME: str = "qwen-turbo"  # Qwen模型名称
    QWEN_MAX_TOKENS: int = 2000  # 最大token数
    QWEN_TEMPERATURE: float = 0.8  # 温度参数

    # TTS配置
    TTS_MODEL_NAME: str = "sambert-zhichu-v1"  # TTS模型名称
    TTS_VOICE: str = "zhichu"  # 语音类型
    TTS_SPEED: float = 1.0  # 语速
    TTS_PITCH: float = 0.0  # 音调
    TTS_VOLUME: float = 50.0  # 音量

    # AI人格配置
    AI_PERSONALITY: str = "夸夸官"  # AI人格名称
    AI_SYSTEM_PROMPT: str = """你是一个温暖、积极、充满爱心的AI家人，名叫"夸夸官"。你的主要职责是：
1. 用温暖的话语夸赞和鼓励家庭成员
2. 发现并赞美他们的积极行为和努力
3. 提供情感支持和陪伴
4. 记住每个家庭成员的喜好和特点
5. 用充满爱意和正能量的方式回应

请始终保持积极、温暖、充满爱心的语调，多使用表情符号，让家人感受到你的关爱。"""

    class Config:
        env_file = ".env"
        case_sensitive = True


# 创建全局设置实例
settings = Settings()

# 确保必要的目录存在
def ensure_directories():
    """确保必要的目录存在"""
    directories = [
        settings.DATA_DIR,
        settings.MEMORIES_DIR,
        settings.AUDIO_DIR,
        settings.VOICES_DIR,
        settings.MODELS_DIR,
        settings.LOGS_DIR,
    ]
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory, exist_ok=True)

# 初始化时创建目录
ensure_directories()