# J_family00 智能家庭助手

## 项目简介

J_family00 是一个基于Python的智能家庭助手系统，专为小型家庭设计。通过语音交互、声纹识别和个性化记忆存储，为家庭成员提供智能化的互动体验。

## 功能特性

- 🎤 **语音识别**: 基于开源模型的中文语音识别
- 🔊 **声纹识别**: 70%匹配度的家庭成员身份识别
- 🧠 **个性化记忆**: 为每个家庭成员维护独立的JSON记忆文件
- 💾 **本地存储**: SQLite数据库 + JSON文件，保护隐私
- 🌐 **前后端分离**: FastAPI后端 + React前端
- 🔒 **隐私保护**: 完全本地部署，数据不出家庭网络

## 技术架构

```
J_family00/
├── backend/           # 后端API服务
│   └── app/
│       ├── api/       # API路由
│       ├── core/      # 核心配置
│       ├── database/  # 数据库操作
│       ├── models/    # 数据模型
│       ├── services/  # 业务服务
│       └── utils/     # 工具函数
├── frontend/          # 前端界面
│   ├── src/
│   └── public/
├── data/             # 数据存储
│   ├── memories/     # JSON记忆文件
│   ├── audio/        # 音频文件
│   └── voices/       # 声纹数据
├── models/           # AI模型文件
├── config/           # 配置文件
├── logs/             # 日志文件
├── docs/             # 文档
└── tests/            # 测试文件
```

## 快速开始

### 环境要求

- Python 3.8+
- Node.js 16+
- 4GB RAM（推荐8GB）

### 后端启动

```bash
cd backend
pip install -r requirements.txt
python main.py
```

### 前端启动

```bash
cd frontend
npm install
npm start
```

## 开发计划

- [x] 项目框架搭建
- [ ] 后端API开发
- [ ] 数据库设计实现
- [ ] 语音识别集成
- [ ] 声纹识别集成
- [ ] 前端界面开发
- [ ] 测试和优化

## 贡献指南

欢迎提交Issue和Pull Request！

## 许可证

MIT License

## 联系方式

- 项目文档: 请查看 [PRD.md](./PRD.md)
- 技术问题: 请提交Issue