"""
Pydantic 数据模式定义
"""

from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum


class MemberStatus(str, Enum):
    """成员状态枚举"""
    ACTIVE = "active"
    INACTIVE = "inactive"


class FileType(str, Enum):
    """文件类型枚举"""
    INPUT = "input"
    OUTPUT = "output"
    VOICE_SAMPLE = "voice_sample"


class LogLevel(str, Enum):
    """日志级别枚举"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"


# 家庭成员相关模式
class FamilyMemberBase(BaseModel):
    """家庭成员基础模式"""
    name: str = Field(..., min_length=1, max_length=50)
    nickname: Optional[str] = Field(None, max_length=50)


class FamilyMemberCreate(FamilyMemberBase):
    """创建家庭成员模式"""
    pass


class FamilyMemberUpdate(BaseModel):
    """更新家庭成员模式"""
    name: Optional[str] = Field(None, min_length=1, max_length=50)
    nickname: Optional[str] = Field(None, max_length=50)
    is_active: Optional[bool] = None


class FamilyMemberResponse(FamilyMemberBase):
    """家庭成员响应模式"""
    id: int
    voice_profile_path: Optional[str]
    memory_file_path: Optional[str]
    is_active: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class FamilyMember(FamilyMemberResponse):
    """家庭成员完整模式"""
    pass


# 对话相关模式
class ConversationBase(BaseModel):
    """对话基础模式"""
    input_text: Optional[str] = None
    response_text: Optional[str] = None
    session_id: Optional[str] = None


class ConversationCreate(ConversationBase):
    """创建对话模式"""
    member_id: Optional[int] = None
    input_audio_path: Optional[str] = None
    confidence_score: Optional[float] = Field(None, ge=0.0, le=1.0)


class ConversationResponse(ConversationBase):
    """对话响应模式"""
    id: int
    member_id: Optional[int]
    input_audio_path: Optional[str]
    response_audio_path: Optional[str]
    confidence_score: Optional[float]
    created_at: datetime

    class Config:
        from_attributes = True


class Conversation(ConversationResponse):
    """对话完整模式"""
    pass


# 语音识别相关模式
class VoiceRecognitionRequest(BaseModel):
    """语音识别请求模式"""
    member_id: Optional[int] = None


class VoiceRecognitionResponse(BaseModel):
    """语音识别响应模式"""
    text: str
    member_id: Optional[int]
    member_name: Optional[str]
    confidence: float = Field(..., ge=0.0, le=1.0)
    audio_path: str


# 声纹相关模式
class VoiceProfileCreate(BaseModel):
    """创建声纹模式"""
    member_id: int


class VoiceProfileResponse(BaseModel):
    """声纹响应模式"""
    id: int
    member_id: int
    model_version: str
    sample_count: int
    is_active: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class VoiceProfile(VoiceProfileResponse):
    """声纹完整模式"""
    pass


# 记忆相关模式
class MemoryType(str, Enum):
    """记忆类型枚举"""
    PREFERENCE = "preference"
    CONVERSATION = "conversation"
    EVENT = "event"
    HABIT = "habit"
    REMINDER = "reminder"


class MemoryEntry(BaseModel):
    """记忆条目模式"""
    id: str
    type: MemoryType
    content: Dict[str, Any]
    keywords: List[str]
    created_at: datetime
    updated_at: datetime


class MemoryCreate(BaseModel):
    """创建记忆模式"""
    type: MemoryType
    content: Dict[str, Any]
    keywords: Optional[List[str]] = []


class MemoryUpdate(BaseModel):
    """更新记忆模式"""
    type: Optional[MemoryType] = None
    content: Optional[Dict[str, Any]] = None
    keywords: Optional[List[str]] = None


class MemoryResponse(MemoryEntry):
    """记忆响应模式"""
    pass


class MemorySearchRequest(BaseModel):
    """记忆搜索请求模式"""
    query: str = Field(..., min_length=1)
    memory_type: Optional[MemoryType] = None
    limit: int = Field(10, ge=1, le=100)


# 音频处理相关模式
class AudioProcessRequest(BaseModel):
    """音频处理请求模式"""
    audio_path: str
    operations: List[str] = []  # 处理操作列表


class AudioProcessResponse(BaseModel):
    """音频处理响应模式"""
    audio_path: str
    duration: Optional[float]
    sample_rate: Optional[int]
    channels: Optional[int]
    format: Optional[str]


class TTSRequest(BaseModel):
    """文本转语音请求模式"""
    text: str = Field(..., min_length=1, max_length=1000)
    voice: str = "default"
    speed: float = Field(1.0, ge=0.5, le=2.0)
    pitch: float = Field(1.0, ge=0.5, le=2.0)


class TTSResponse(BaseModel):
    """文本转语音响应模式"""
    audio_path: str
    text: str
    voice: str


# 音频文件相关模式
class AudioFileResponse(BaseModel):
    """音频文件响应模式"""
    id: int
    member_id: Optional[int]
    file_path: str
    file_name: str
    file_size: int
    duration: Optional[float]
    sample_rate: Optional[int]
    channels: Optional[int]
    format: Optional[str]
    file_type: FileType
    created_at: datetime

    class Config:
        from_attributes = True