{"name": "j-family00-frontend", "version": "0.1.0", "private": true, "description": "J_family00 智能家庭助手前端界面", "dependencies": {"@ant-design/icons": "^5.2.0", "@types/node": "^20.5.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "antd": "^5.10.0", "axios": "^1.5.0", "dayjs": "^1.11.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.15.0", "react-scripts": "5.0.1", "socket.io-client": "^4.7.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "dev": "react-scripts start"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/jest": "^29.5.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.45.0", "prettier": "^3.0.0", "typescript": "^5.9.2"}, "proxy": "http://localhost:8000"}