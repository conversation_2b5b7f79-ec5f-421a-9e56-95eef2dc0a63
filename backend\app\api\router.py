"""
API 路由主文件
"""

from fastapi import APIRouter
from app.api.endpoints import (
    health,
    members,
    conversations,
    voice,
    memory,
    audio,
    config
)

api_router = APIRouter()

# 包含各个子路由
api_router.include_router(health.router, prefix="/health", tags=["健康检查"])
api_router.include_router(members.router, prefix="/members", tags=["家庭成员"])
api_router.include_router(conversations.router, prefix="/conversations", tags=["对话管理"])
api_router.include_router(voice.router, prefix="/voice", tags=["语音识别"])
api_router.include_router(memory.router, prefix="/memory", tags=["记忆管理"])
api_router.include_router(audio.router, prefix="/audio", tags=["音频处理"])
api_router.include_router(config.router, prefix="/config", tags=["配置管理"])