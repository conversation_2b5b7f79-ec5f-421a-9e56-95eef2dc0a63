"""
数据库初始化脚本
"""

import asyncio
import os
from sqlalchemy import create_engine
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from app.models.database import Base, FamilyMember
from app.core.config import settings
from app.database.connection import async_session_maker


async def create_tables():
    """创建数据库表"""
    # 创建异步引擎
    engine = create_async_engine(
        settings.DATABASE_URL.replace("sqlite:///", "sqlite+aiosqlite:///"),
        echo=settings.DEBUG
    )
    
    # 创建所有表
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    await engine.dispose()
    print("✅ 数据库表创建成功")


async def create_default_member():
    """创建默认的AI家人成员"""
    async with async_session_maker() as session:
        # 检查是否已存在AI家人
        from app.database.member_dao import FamilyMemberDAO
        member_dao = FamilyMemberDAO(session)
        
        ai_member = await member_dao.get_by_name("AI家人")
        if ai_member:
            print("✅ AI家人成员已存在")
            return ai_member
        
        # 创建AI家人成员
        ai_member_data = {
            "name": "AI家人",
            "nickname": "夸夸官",
            "is_active": True
        }
        
        ai_member = await member_dao.create(ai_member_data)
        await session.commit()
        
        print("✅ 创建默认AI家人成员成功")
        return ai_member


async def initialize_ai_memory():
    """初始化AI家人的记忆文件"""
    from app.services.memory.memory_service import MemoryService
    
    memory_service = MemoryService()
    
    # 为AI家人创建记忆文件
    success = await memory_service.initialize_member_memory(0, "AI家人")
    if success:
        print("✅ AI家人记忆文件初始化成功")
    else:
        print("⚠️ AI家人记忆文件已存在")


async def create_sample_data():
    """创建示例数据"""
    async with async_session_maker() as session:
        from app.database.member_dao import FamilyMemberDAO
        member_dao = FamilyMemberDAO(session)
        
        # 创建示例家庭成员
        sample_members = [
            {"name": "爸爸", "nickname": "老爸", "is_active": True},
            {"name": "妈妈", "nickname": "老妈", "is_active": True},
            {"name": "小明", "nickname": "明明", "is_active": True},
        ]
        
        for member_data in sample_members:
            existing = await member_dao.get_by_name(member_data["name"])
            if not existing:
                member = await member_dao.create(member_data)
                print(f"✅ 创建示例成员: {member.name}")
                
                # 为每个成员初始化记忆文件
                from app.services.memory.memory_service import MemoryService
                memory_service = MemoryService()
                await memory_service.initialize_member_memory(member.id, member.name)
        
        await session.commit()


async def init_database():
    """完整的数据库初始化流程"""
    print("🚀 开始初始化数据库...")
    
    # 确保数据目录存在
    os.makedirs(os.path.dirname(settings.DATABASE_URL.replace("sqlite:///", "")), exist_ok=True)
    
    try:
        # 1. 创建数据库表
        await create_tables()
        
        # 2. 创建默认AI家人成员
        await create_default_member()
        
        # 3. 初始化AI家人记忆
        await initialize_ai_memory()
        
        # 4. 创建示例数据（可选）
        await create_sample_data()
        
        print("🎉 数据库初始化完成！")
        
    except Exception as e:
        print(f"❌ 数据库初始化失败: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(init_database())
