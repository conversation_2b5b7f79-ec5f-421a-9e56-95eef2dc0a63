"""
Whisper语音识别引擎
"""

import os
import asyncio
from typing import Optional, Tuple, Dict, Any
from pathlib import Path
import whisper
import torch
from app.core.config import settings
from app.core.config_manager import config_manager


class WhisperEngine:
    """Whisper语音识别引擎"""
    
    def __init__(self):
        self.model = None
        self.model_name = config_manager.get('speech_recognition.whisper_model', 'base')
        self.device = self._get_device()
        self._is_initialized = False
    
    def _get_device(self) -> str:
        """获取最佳设备"""
        if torch.cuda.is_available():
            return "cuda"
        elif hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
            return "mps"  # Apple Silicon
        else:
            return "cpu"
    
    async def initialize(self) -> bool:
        """初始化Whisper模型"""
        if self._is_initialized:
            return True
        
        try:
            print(f"正在加载Whisper模型: {self.model_name} (设备: {self.device})")
            
            # 在线程池中加载模型以避免阻塞
            loop = asyncio.get_event_loop()
            self.model = await loop.run_in_executor(
                None, 
                whisper.load_model, 
                self.model_name, 
                self.device
            )
            
            self._is_initialized = True
            print(f"Whisper模型加载成功: {self.model_name}")
            return True
            
        except Exception as e:
            print(f"Whisper模型加载失败: {e}")
            return False
    
    async def transcribe(
        self, 
        audio_path: str, 
        language: Optional[str] = "zh"
    ) -> Tuple[str, float, Dict[str, Any]]:
        """
        转录音频文件
        
        Args:
            audio_path: 音频文件路径
            language: 语言代码，默认为中文
            
        Returns:
            tuple: (转录文本, 置信度, 详细信息)
        """
        if not self._is_initialized:
            if not await self.initialize():
                raise RuntimeError("Whisper模型初始化失败")
        
        if not os.path.exists(audio_path):
            raise FileNotFoundError(f"音频文件不存在: {audio_path}")
        
        try:
            # 在线程池中执行转录以避免阻塞
            loop = asyncio.get_event_loop()
            
            # 设置转录选项
            options = {
                "language": language,
                "task": "transcribe",
                "fp16": False if self.device == "cpu" else True,
                "verbose": False
            }
            
            # 执行转录
            result = await loop.run_in_executor(
                None,
                lambda: self.model.transcribe(audio_path, **options)
            )
            
            # 提取结果
            text = result.get("text", "").strip()
            
            # 计算平均置信度
            segments = result.get("segments", [])
            if segments:
                # 使用segments中的平均概率作为置信度
                avg_confidence = sum(
                    segment.get("avg_logprob", 0) for segment in segments
                ) / len(segments)
                # 将log概率转换为0-1之间的置信度
                confidence = max(0.0, min(1.0, (avg_confidence + 1.0) / 2.0))
            else:
                confidence = 0.5  # 默认置信度
            
            # 详细信息
            details = {
                "language": result.get("language", language),
                "duration": self._get_audio_duration(audio_path),
                "segments_count": len(segments),
                "model": self.model_name,
                "device": self.device
            }
            
            return text, confidence, details
            
        except Exception as e:
            print(f"Whisper转录失败: {e}")
            raise RuntimeError(f"语音转录失败: {str(e)}")
    
    def _get_audio_duration(self, audio_path: str) -> float:
        """获取音频时长"""
        try:
            import librosa
            y, sr = librosa.load(audio_path)
            duration = librosa.get_duration(y=y, sr=sr)
            return duration
        except Exception:
            return 0.0
    
    async def transcribe_realtime(
        self, 
        audio_chunks: list, 
        language: Optional[str] = "zh"
    ) -> str:
        """
        实时转录音频块（未实现，预留接口）
        
        Args:
            audio_chunks: 音频数据块列表
            language: 语言代码
            
        Returns:
            转录文本
        """
        # TODO: 实现实时转录功能
        raise NotImplementedError("实时转录功能尚未实现")
    
    def get_supported_languages(self) -> Dict[str, str]:
        """获取支持的语言列表"""
        return {
            "zh": "中文",
            "en": "English",
            "ja": "日本語", 
            "ko": "한국어",
            "es": "Español",
            "fr": "Français",
            "de": "Deutsch",
            "it": "Italiano",
            "pt": "Português",
            "ru": "Русский"
        }
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return {
            "name": "OpenAI Whisper",
            "model_size": self.model_name,
            "device": self.device,
            "initialized": self._is_initialized,
            "supported_languages": list(self.get_supported_languages().keys()),
            "capabilities": [
                "多语言识别",
                "高精度转录", 
                "噪音鲁棒性",
                "标点符号自动添加"
            ]
        }
    
    def cleanup(self):
        """清理资源"""
        if self.model is not None:
            del self.model
            self.model = None
        
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        self._is_initialized = False
        print("Whisper引擎资源已清理")