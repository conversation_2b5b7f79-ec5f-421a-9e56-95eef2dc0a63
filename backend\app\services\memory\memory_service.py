"""
记忆管理服务类
"""

import json
import uuid
from typing import Dict, List, Optional, Any
from datetime import datetime
from app.services.memory.json_store import JSONMemoryStore
from app.services.memory.memory_search import MemorySearchEngine
from app.models.schemas import MemoryCreate, MemoryUpdate, MemoryResponse, MemoryType


class MemoryService:
    """记忆管理服务"""
    
    def __init__(self):
        self.store = JSONMemoryStore()
        self.search_engine = MemorySearchEngine()
    
    async def initialize_member_memory(self, member_id: int, member_name: str) -> bool:
        """为新成员初始化记忆文件"""
        if await self.store.memory_file_exists(member_id, member_name):
            return True  # 已存在
        
        return await self.store.create_memory_file(member_id, member_name)
    
    async def add_memory(self, member_id: int, memory_data: MemoryCreate) -> MemoryResponse:
        """添加新记忆"""
        # 首先获取成员信息（这里简化处理，实际应从数据库获取）
        member_name = f"member_{member_id}"  # 临时方案
        
        # 确保记忆文件存在
        if not await self.store.memory_file_exists(member_id, member_name):
            await self.initialize_member_memory(member_id, member_name)
        
        # 加载现有记忆
        data = await self.store.get_member_memories(member_id, member_name)
        if not data:
            raise Exception("无法加载记忆文件")
        
        # 创建新记忆条目
        memory_id = str(uuid.uuid4())
        now = datetime.utcnow().isoformat()
        
        memory_entry = {
            "id": memory_id,
            "type": memory_data.type.value,
            "content": memory_data.content,
            "keywords": memory_data.keywords or [],
            "created_at": now,
            "updated_at": now
        }
        
        # 根据类型添加到相应的分类
        memories = data["memories"]
        if memory_data.type == MemoryType.PREFERENCE:
            if "preferences" not in memories:
                memories["preferences"] = {}
            # 对于偏好，直接合并到偏好对象中
            if isinstance(memory_data.content, dict):
                memories["preferences"].update(memory_data.content)
        
        elif memory_data.type == MemoryType.CONVERSATION:
            if "conversations" not in memories:
                memories["conversations"] = []
            memories["conversations"].append(memory_entry)
        
        elif memory_data.type == MemoryType.EVENT:
            if "events" not in memories:
                memories["events"] = []
            memories["events"].append(memory_entry)
        
        elif memory_data.type == MemoryType.HABIT:
            if "habits" not in memories:
                memories["habits"] = {}
            # 对于习惯，合并到habits对象中
            if isinstance(memory_data.content, dict):
                memories["habits"].update(memory_data.content)
        
        elif memory_data.type == MemoryType.REMINDER:
            if "reminders" not in memories:
                memories["reminders"] = []
            memories["reminders"].append(memory_entry)
        
        # 保存更新后的数据
        await self.store._save_memory_file(
            self.store._get_file_path(member_id, member_name), 
            data
        )
        
        return MemoryResponse(
            id=memory_id,
            type=memory_data.type,
            content=memory_data.content,
            keywords=memory_data.keywords or [],
            created_at=datetime.fromisoformat(now),
            updated_at=datetime.fromisoformat(now)
        )
    
    async def search_memories(
        self, 
        member_id: int, 
        query: str, 
        memory_type: Optional[str] = None,
        limit: int = 10
    ) -> List[MemoryResponse]:
        """搜索记忆"""
        member_name = f"member_{member_id}"
        
        data = await self.store.get_member_memories(member_id, member_name)
        if not data:
            return []
        
        # 使用搜索引擎搜索
        results = await self.search_engine.search(
            data["memories"], 
            query, 
            memory_type, 
            limit
        )
        
        return [self._convert_to_memory_response(result) for result in results]
    
    async def get_member_memories(
        self, 
        member_id: int,
        memory_type: Optional[str] = None,
        skip: int = 0,
        limit: int = 50
    ) -> List[MemoryResponse]:
        """获取成员的记忆列表"""
        member_name = f"member_{member_id}"
        
        data = await self.store.get_member_memories(member_id, member_name)
        if not data:
            return []
        
        memories = data["memories"]
        results = []
        
        # 根据类型过滤记忆
        if memory_type:
            if memory_type == "conversation" and "conversations" in memories:
                results.extend(memories["conversations"])
            elif memory_type == "event" and "events" in memories:
                results.extend(memories["events"])
            elif memory_type == "reminder" and "reminders" in memories:
                results.extend(memories["reminders"])
        else:
            # 获取所有类型的记忆
            for key in ["conversations", "events", "reminders"]:
                if key in memories:
                    results.extend(memories[key])
        
        # 按创建时间排序
        results.sort(key=lambda x: x.get("created_at", ""), reverse=True)
        
        # 分页
        results = results[skip:skip + limit]
        
        return [self._convert_to_memory_response(result) for result in results]
    
    async def update_memory(
        self, 
        member_id: int, 
        memory_id: str, 
        memory_update: MemoryUpdate
    ) -> Optional[MemoryResponse]:
        """更新记忆"""
        member_name = f"member_{member_id}"
        
        data = await self.store.get_member_memories(member_id, member_name)
        if not data:
            return None
        
        memories = data["memories"]
        updated_memory = None
        
        # 在各个类型中查找并更新记忆
        for category in ["conversations", "events", "reminders"]:
            if category in memories:
                for memory in memories[category]:
                    if memory.get("id") == memory_id:
                        # 更新记忆
                        if memory_update.type:
                            memory["type"] = memory_update.type.value
                        if memory_update.content:
                            memory["content"] = memory_update.content
                        if memory_update.keywords is not None:
                            memory["keywords"] = memory_update.keywords
                        
                        memory["updated_at"] = datetime.utcnow().isoformat()
                        updated_memory = memory
                        break
                
                if updated_memory:
                    break
        
        if updated_memory:
            # 保存更新后的数据
            await self.store._save_memory_file(
                self.store._get_file_path(member_id, member_name),
                data
            )
            return self._convert_to_memory_response(updated_memory)
        
        return None
    
    async def delete_memory(self, member_id: int, memory_id: str) -> bool:
        """删除记忆"""
        member_name = f"member_{member_id}"
        
        data = await self.store.get_member_memories(member_id, member_name)
        if not data:
            return False
        
        memories = data["memories"]
        deleted = False
        
        # 在各个类型中查找并删除记忆
        for category in ["conversations", "events", "reminders"]:
            if category in memories:
                original_length = len(memories[category])
                memories[category] = [
                    memory for memory in memories[category] 
                    if memory.get("id") != memory_id
                ]
                if len(memories[category]) < original_length:
                    deleted = True
                    break
        
        if deleted:
            # 保存更新后的数据
            await self.store._save_memory_file(
                self.store._get_file_path(member_id, member_name),
                data
            )
        
        return deleted
    
    async def get_memory_stats(self, member_id: int) -> Optional[Dict[str, Any]]:
        """获取记忆统计信息"""
        member_name = f"member_{member_id}"
        return await self.store.get_memory_stats(member_id, member_name)
    
    async def backup_memories(self, member_id: int) -> Optional[str]:
        """备份记忆文件"""
        member_name = f"member_{member_id}"
        return await self.store.backup_memory_file(member_id, member_name)
    
    def _convert_to_memory_response(self, memory_data: Dict[str, Any]) -> MemoryResponse:
        """将内部记忆数据转换为响应格式"""
        return MemoryResponse(
            id=memory_data.get("id", ""),
            type=MemoryType(memory_data.get("type", "conversation")),
            content=memory_data.get("content", {}),
            keywords=memory_data.get("keywords", []),
            created_at=datetime.fromisoformat(memory_data.get("created_at", datetime.utcnow().isoformat())),
            updated_at=datetime.fromisoformat(memory_data.get("updated_at", datetime.utcnow().isoformat()))
        )