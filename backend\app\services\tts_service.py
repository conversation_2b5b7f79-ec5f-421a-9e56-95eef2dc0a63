"""
TTS语音合成服务管理类
"""

import os
from typing import Dict, Optional, Any
from pathlib import Path
from app.services.tts.dashscope_tts import DashScopeTTSService
from app.core.config import settings
from app.core.logger import get_logger
from app.core.exceptions import ModelInitializationError

logger = get_logger("tts")


class TTSService:
    """TTS语音合成服务管理类"""
    
    def __init__(self):
        self.engine = None
        self.engine_type = "dashscope"  # 默认使用阿里百炼
        self._initialize_engine()
    
    def _initialize_engine(self):
        """初始化TTS引擎"""
        try:
            if self.engine_type == "dashscope":
                self.engine = DashScopeTTSService()
            else:
                # 默认使用阿里百炼
                self.engine = DashScopeTTSService()
            
            logger.info(f"TTS引擎初始化成功: {self.engine_type}")
            
        except Exception as e:
            logger.error(f"TTS引擎初始化失败: {str(e)}")
            raise ModelInitializationError(f"TTS引擎初始化失败: {str(e)}", model_name=self.engine_type)
    
    async def text_to_speech(
        self,
        text: str,
        voice: Optional[str] = None,
        speed: Optional[float] = None,
        pitch: Optional[float] = None,
        volume: Optional[float] = None,
        output_format: str = "wav"
    ) -> Dict[str, Any]:
        """
        文本转语音
        
        Args:
            text: 要合成的文本
            voice: 语音类型
            speed: 语速
            pitch: 音调
            volume: 音量
            output_format: 输出格式
            
        Returns:
            包含音频文件信息的字典
        """
        if not self.engine:
            raise ModelInitializationError("TTS引擎未初始化", model_name=self.engine_type)
        
        try:
            result = await self.engine.synthesize_speech(
                text=text,
                voice=voice,
                speed=speed,
                pitch=pitch,
                volume=volume,
                output_format=output_format
            )
            
            logger.info(f"TTS合成成功: {result.get('filename')}")
            return result
            
        except Exception as e:
            logger.error(f"TTS合成失败: {str(e)}")
            raise
    
    async def get_available_voices(self) -> Dict[str, Any]:
        """获取可用的语音列表"""
        if not self.engine:
            return {"voices": {}, "error": "TTS引擎未初始化"}
        
        return await self.engine.get_available_voices()
    
    async def cleanup_old_files(self, max_age_hours: int = 24) -> int:
        """清理旧的音频文件"""
        if not self.engine:
            return 0
        
        return await self.engine.cleanup_old_files(max_age_hours)
    
    async def get_service_status(self) -> Dict[str, Any]:
        """获取服务状态"""
        if not self.engine:
            return {
                "engine": self.engine_type,
                "status": "not_initialized",
                "error": "TTS引擎未初始化"
            }
        
        service_info = await self.engine.get_service_info()
        service_info["engine"] = self.engine_type
        return service_info
    
    def get_audio_url(self, audio_path: str) -> str:
        """获取音频文件的访问URL"""
        try:
            # 将绝对路径转换为相对于audio目录的路径
            audio_dir = Path(settings.AUDIO_DIR)
            file_path = Path(audio_path)
            
            if file_path.is_absolute():
                relative_path = file_path.relative_to(audio_dir)
            else:
                relative_path = file_path
            
            # 构建URL路径
            url_path = f"/api/v1/audio/files/{relative_path.as_posix()}"
            return url_path
            
        except Exception as e:
            logger.warning(f"生成音频URL失败: {str(e)}")
            return ""
