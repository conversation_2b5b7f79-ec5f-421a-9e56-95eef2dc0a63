@echo off
echo =====================================
echo  J_family00 开发环境安装脚本
echo =====================================
echo.

:: 检查管理员权限
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo [警告] 建议以管理员身份运行此脚本以避免权限问题
    echo.
)

:: 检查Python是否安装
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 未检测到Python，请先安装Python 3.8+
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

:: 检查Node.js是否安装
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 未检测到Node.js，请先安装Node.js 16+
    echo 下载地址: https://nodejs.org/
    pause
    exit /b 1
)

:: 设置工作目录
cd /d "%~dp0"

:: 创建Python虚拟环境
echo [信息] 创建Python虚拟环境...
if exist "venv" (
    echo [提示] 虚拟环境已存在，跳过创建
) else (
    python -m venv venv
)

:: 激活虚拟环境
echo [信息] 激活Python虚拟环境...
call venv\Scripts\activate.bat

:: 升级pip
echo [信息] 升级pip...
python -m pip install --upgrade pip

:: 安装后端依赖
echo [信息] 安装后端依赖...
cd backend
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo [错误] 后端依赖安装失败
    pause
    exit /b 1
)

:: 创建环境配置文件
if not exist ".env" (
    echo [信息] 创建环境配置文件...
    copy .env.example .env
    echo [提示] 请根据需要修改 .env 文件中的配置
)

:: 初始化数据库
echo [信息] 初始化数据库...
python -c "
import asyncio
from app.database.connection import db_manager
asyncio.run(db_manager.init_db())
print('数据库初始化完成')
"

:: 安装前端依赖
echo [信息] 安装前端依赖...
cd ..\frontend
npm install
if %errorlevel% neq 0 (
    echo [错误] 前端依赖安装失败
    pause
    exit /b 1
)

:: 创建必要目录
echo [信息] 创建必要目录...
cd ..
if not exist "data" mkdir data
if not exist "data\memories" mkdir data\memories
if not exist "data\audio" mkdir data\audio
if not exist "data\voices" mkdir data\voices
if not exist "models" mkdir models
if not exist "logs" mkdir logs

echo.
echo =====================================
echo  开发环境安装完成！
echo =====================================
echo.
echo 后续步骤：
echo 1. 修改 backend\.env 文件中的配置（如需要）
echo 2. 运行 start.bat 启动开发服务器
echo 3. 访问 http://localhost:3000 查看前端界面
echo 4. 访问 http://localhost:8000/docs 查看API文档
echo.
pause