"""
家庭成员API测试
"""

import pytest
from httpx import AsyncClient


class TestMembersAPI:
    """家庭成员API测试类"""
    
    @pytest.mark.asyncio
    async def test_create_member(self, client: AsyncClient, sample_member_data):
        """测试创建家庭成员"""
        response = await client.post("/api/v1/members/", json=sample_member_data)
        
        assert response.status_code == 201
        data = response.json()
        
        assert data["name"] == sample_member_data["name"]
        assert data["nickname"] == sample_member_data["nickname"]
        assert data["is_active"] == sample_member_data["is_active"]
        assert "id" in data
        assert "created_at" in data
    
    @pytest.mark.asyncio
    async def test_get_members_list(self, client: AsyncClient, sample_member_data):
        """测试获取家庭成员列表"""
        # 先创建一个成员
        await client.post("/api/v1/members/", json=sample_member_data)
        
        # 获取成员列表
        response = await client.get("/api/v1/members/")
        
        assert response.status_code == 200
        data = response.json()
        
        assert isinstance(data, list)
        assert len(data) >= 1
        
        # 检查第一个成员的数据
        member = data[0]
        assert "id" in member
        assert "name" in member
        assert "is_active" in member
    
    @pytest.mark.asyncio
    async def test_get_member_by_id(self, client: AsyncClient, sample_member_data):
        """测试根据ID获取家庭成员"""
        # 先创建一个成员
        create_response = await client.post("/api/v1/members/", json=sample_member_data)
        created_member = create_response.json()
        member_id = created_member["id"]
        
        # 根据ID获取成员
        response = await client.get(f"/api/v1/members/{member_id}")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["id"] == member_id
        assert data["name"] == sample_member_data["name"]
        assert data["nickname"] == sample_member_data["nickname"]
    
    @pytest.mark.asyncio
    async def test_update_member(self, client: AsyncClient, sample_member_data):
        """测试更新家庭成员信息"""
        # 先创建一个成员
        create_response = await client.post("/api/v1/members/", json=sample_member_data)
        created_member = create_response.json()
        member_id = created_member["id"]
        
        # 更新成员信息
        update_data = {
            "nickname": "新昵称",
            "is_active": False
        }
        
        response = await client.put(f"/api/v1/members/{member_id}", json=update_data)
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["id"] == member_id
        assert data["nickname"] == update_data["nickname"]
        assert data["is_active"] == update_data["is_active"]
        assert data["name"] == sample_member_data["name"]  # 名字不应该改变
    
    @pytest.mark.asyncio
    async def test_delete_member(self, client: AsyncClient, sample_member_data):
        """测试删除家庭成员"""
        # 先创建一个成员
        create_response = await client.post("/api/v1/members/", json=sample_member_data)
        created_member = create_response.json()
        member_id = created_member["id"]
        
        # 删除成员
        response = await client.delete(f"/api/v1/members/{member_id}")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["message"] == "Member deleted successfully"
        
        # 验证成员已被删除
        get_response = await client.get(f"/api/v1/members/{member_id}")
        assert get_response.status_code == 404
    
    @pytest.mark.asyncio
    async def test_create_member_validation_error(self, client: AsyncClient):
        """测试创建成员时的验证错误"""
        # 缺少必需字段
        invalid_data = {
            "nickname": "只有昵称"
        }
        
        response = await client.post("/api/v1/members/", json=invalid_data)
        
        assert response.status_code == 422
        data = response.json()
        
        assert "error_code" in data
        assert data["error_code"] == "VALIDATION_ERROR"
    
    @pytest.mark.asyncio
    async def test_get_nonexistent_member(self, client: AsyncClient):
        """测试获取不存在的成员"""
        response = await client.get("/api/v1/members/99999")
        
        assert response.status_code == 404
        data = response.json()
        
        assert "error_code" in data
        assert data["error_code"] == "RESOURCE_NOT_FOUND"
