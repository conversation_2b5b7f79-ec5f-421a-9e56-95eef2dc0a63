import React, { useState, useRef, useEffect } from 'react';
import { Button, message } from 'antd';
import { AudioOutlined, StopOutlined } from '@ant-design/icons';

// 添加CSS动画
const pulseAnimation = `
  @keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
  }
`;

// 注入CSS
if (typeof document !== 'undefined') {
  const style = document.createElement('style');
  style.textContent = pulseAnimation;
  document.head.appendChild(style);
}

interface RealTimeVoiceInputProps {
  onTranscript: (text: string) => void;
  disabled?: boolean;
}

const RealTimeVoiceInput: React.FC<RealTimeVoiceInputProps> = ({
  onTranscript,
  disabled = false
}) => {
  const [isListening, setIsListening] = useState(false);
  const [transcript, setTranscript] = useState('');
  const recognitionRef = useRef<any>(null);

  useEffect(() => {
    // 检查浏览器是否支持语音识别
    if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
      message.error('您的浏览器不支持语音识别功能，请使用Chrome浏览器');
      return;
    }

    // 创建语音识别实例
    const SpeechRecognition = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition;
    const recognition = new SpeechRecognition();

    // 配置语音识别
    recognition.continuous = false; // 不连续识别
    recognition.interimResults = true; // 显示中间结果
    recognition.lang = 'zh-CN'; // 中文识别

    // 识别结果处理
    recognition.onresult = (event: any) => {
      let finalTranscript = '';
      let interimTranscript = '';

      for (let i = event.resultIndex; i < event.results.length; i++) {
        const transcript = event.results[i][0].transcript;
        if (event.results[i].isFinal) {
          finalTranscript += transcript;
        } else {
          interimTranscript += transcript;
        }
      }

      // 更新显示的文本
      setTranscript(finalTranscript || interimTranscript);

      // 如果有最终结果，传递给父组件
      if (finalTranscript) {
        onTranscript(finalTranscript);
        setTranscript('');
      }
    };

    // 识别开始
    recognition.onstart = () => {
      setIsListening(true);
      message.success('开始语音识别，请说话...');
    };

    // 识别结束
    recognition.onend = () => {
      setIsListening(false);
    };

    // 识别错误
    recognition.onerror = (event: any) => {
      setIsListening(false);
      console.error('语音识别错误:', event.error);
      
      switch (event.error) {
        case 'no-speech':
          message.warning('没有检测到语音，请重试');
          break;
        case 'audio-capture':
          message.error('无法访问麦克风，请检查权限');
          break;
        case 'not-allowed':
          message.error('麦克风权限被拒绝，请允许访问麦克风');
          break;
        default:
          message.error('语音识别失败，请重试');
      }
    };

    recognitionRef.current = recognition;

    return () => {
      if (recognitionRef.current) {
        recognitionRef.current.stop();
      }
    };
  }, [onTranscript]);

  const startListening = () => {
    if (recognitionRef.current && !isListening) {
      try {
        recognitionRef.current.start();
      } catch (error) {
        console.error('启动语音识别失败:', error);
        message.error('启动语音识别失败');
      }
    }
  };

  const stopListening = () => {
    if (recognitionRef.current && isListening) {
      recognitionRef.current.stop();
    }
  };

  return (
    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
      <Button
        type={isListening ? "primary" : "default"}
        danger={isListening}
        icon={isListening ? <StopOutlined /> : <AudioOutlined />}
        onClick={isListening ? stopListening : startListening}
        disabled={disabled}
        style={{
          backgroundColor: isListening ? '#ff4d4f' : undefined,
          borderColor: isListening ? '#ff4d4f' : undefined,
          animation: isListening ? 'pulse 1.5s infinite' : undefined
        }}
      >
        {isListening ? '停止' : '语音'}
      </Button>
      
      {transcript && (
        <span style={{ 
          color: '#1890ff', 
          fontStyle: 'italic',
          maxWidth: '200px',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          whiteSpace: 'nowrap'
        }}>
          {transcript}
        </span>
      )}
    </div>
  );
};

export default RealTimeVoiceInput;
