"""
数据库连接和会话管理
"""

from sqlalchemy import create_engine
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
from app.core.config import settings
from app.models.database import Base


# 创建异步数据库引擎
engine = create_async_engine(
    settings.DATABASE_URL.replace("sqlite://", "sqlite+aiosqlite://"),
    echo=settings.DEBUG,
    future=True
)

# 创建异步会话工厂
async_session_maker = sessionmaker(
    engine, class_=AsyncSession, expire_on_commit=False
)


async def create_tables():
    """创建数据库表"""
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)


async def get_db_session() -> AsyncSession:
    """获取数据库会话"""
    async with async_session_maker() as session:
        try:
            yield session
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        self.engine = engine
        self.async_session_maker = async_session_maker
    
    async def init_db(self):
        """初始化数据库"""
        await create_tables()
    
    async def get_session(self) -> AsyncSession:
        """获取数据库会话"""
        async with self.async_session_maker() as session:
            return session
    
    async def close(self):
        """关闭数据库连接"""
        await self.engine.dispose()


# 全局数据库管理器实例
db_manager = DatabaseManager()