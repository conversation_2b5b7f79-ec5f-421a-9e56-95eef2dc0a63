"""
语音识别服务
"""

import os
import uuid
import aiofiles
from typing import Optional, Tu<PERSON>, Dict, Any
from pathlib import Path
from fastapi import UploadFile
from app.core.config import settings
from app.core.config_manager import config_manager
from app.services.voice.sherpa_ncnn_engine import SherpaNcnnEngine
from app.models.database import FamilyMember


class SpeechRecognitionService:
    """语音识别服务"""
    
    def __init__(self):
        self.engine = None
        self.engine_type = config_manager.get('speech_recognition.engine', 'sherpa-ncnn')
        self._initialize_engine()
    
    def _initialize_engine(self):
        """初始化语音识别引擎"""
        if self.engine_type == 'sherpa-ncnn':
            self.engine = SherpaNcnnEngine()
        else:
            # 默认使用Sherpa-ncnn
            self.engine = SherpaNcnnEngine()
    
    async def save_audio_file(self, audio_file: UploadFile, member_id: Optional[int] = None) -> str:
        """保存音频文件"""
        try:
            # 生成唯一文件名
            file_extension = audio_file.filename.split('.')[-1] if '.' in audio_file.filename else 'wav'
            unique_filename = f"{uuid.uuid4()}.{file_extension}"
            
            # 构建保存路径
            if member_id:
                save_dir = Path(settings.AUDIO_DIR) / f"member_{member_id}"
            else:
                save_dir = Path(settings.AUDIO_DIR) / "temp"
            
            save_dir.mkdir(parents=True, exist_ok=True)
            audio_path = save_dir / unique_filename
            
            # 保存文件
            async with aiofiles.open(audio_path, 'wb') as f:
                content = await audio_file.read()
                await f.write(content)
            
            return str(audio_path)
            
        except Exception as e:
            raise RuntimeError(f"保存音频文件失败: {str(e)}")
    
    async def recognize_speech(
        self, 
        audio_path: str, 
        language: Optional[str] = "zh"
    ) -> Dict[str, Any]:
        """
        语音识别
        
        Args:
            audio_path: 音频文件路径
            language: 语言代码
            
        Returns:
            识别结果字典
        """
        try:
            # 初始化引擎（如果尚未初始化）
            if not self.engine._is_initialized:
                success = await self.engine.initialize()
                if not success:
                    raise RuntimeError("语音识别引擎初始化失败")
            
            # 执行语音识别
            text, confidence, details = await self.engine.transcribe(
                audio_path=audio_path,
                language=language
            )
            
            return {
                "text": text,
                "confidence": confidence,
                "language": language,
                "details": details,
                "engine": self.engine_type,
                "success": True
            }
            
        except Exception as e:
            print(f"语音识别失败: {e}")
            return {
                "text": "",
                "confidence": 0.0,
                "language": language,
                "error": str(e),
                "engine": self.engine_type,
                "success": False
            }
    
    async def recognize_realtime(
        self, 
        audio_chunk: bytes, 
        is_final: bool = False
    ) -> Dict[str, Any]:
        """
        实时语音识别
        
        Args:
            audio_chunk: 音频数据块
            is_final: 是否为最终数据
            
        Returns:
            识别结果字典
        """
        try:
            # 初始化引擎（如果尚未初始化）
            if not self.engine._is_initialized:
                success = await self.engine.initialize()
                if not success:
                    raise RuntimeError("语音识别引擎初始化失败")
            
            # 将字节数据转换为numpy数组
            import numpy as np
            audio_array = np.frombuffer(audio_chunk, dtype=np.float32)
            
            # 执行实时识别
            text, is_complete = await self.engine.transcribe_realtime(
                audio_chunk=audio_array,
                is_final=is_final
            )
            
            return {
                "text": text,
                "is_complete": is_complete,
                "is_final": is_final,
                "engine": self.engine_type,
                "success": True
            }
            
        except Exception as e:
            print(f"实时语音识别失败: {e}")
            return {
                "text": "",
                "is_complete": False,
                "is_final": is_final,
                "error": str(e),
                "engine": self.engine_type,
                "success": False
            }
    
    async def get_supported_languages(self) -> Dict[str, str]:
        """获取支持的语言列表"""
        if self.engine:
            return self.engine.get_supported_languages()
        return {"zh": "中文"}
    
    async def get_engine_info(self) -> Dict[str, Any]:
        """获取引擎信息"""
        if self.engine:
            return self.engine.get_model_info()
        return {
            "name": "Unknown",
            "initialized": False,
            "error": "引擎未初始化"
        }
    
    def reset_realtime_session(self):
        """重置实时识别会话"""
        if self.engine and hasattr(self.engine, 'reset_realtime_stream'):
            self.engine.reset_realtime_stream()
    
    def cleanup(self):
        """清理资源"""
        if self.engine:
            self.engine.cleanup()
    
    async def validate_audio_file(self, audio_path: str) -> Dict[str, Any]:
        """验证音频文件格式"""
        try:
            import librosa
            
            # 读取音频文件信息
            y, sr = librosa.load(audio_path, sr=None)
            duration = librosa.get_duration(y=y, sr=sr)
            
            # 检查文件大小
            file_size = os.path.getsize(audio_path)
            max_size = config_manager.get('audio.max_file_size', 50 * 1024 * 1024)  # 50MB
            
            # 检查音频时长
            max_duration = config_manager.get('audio.max_duration', 300)  # 5分钟
            
            issues = []
            if file_size > max_size:
                issues.append(f"文件大小超出限制: {file_size / 1024 / 1024:.1f}MB > {max_size / 1024 / 1024:.1f}MB")
            
            if duration > max_duration:
                issues.append(f"音频时长超出限制: {duration:.1f}s > {max_duration}s")
            
            if duration < 0.1:
                issues.append("音频时长过短")
            
            return {
                "valid": len(issues) == 0,
                "issues": issues,
                "info": {
                    "duration": duration,
                    "sample_rate": sr,
                    "channels": 1 if len(y.shape) == 1 else y.shape[1],
                    "file_size": file_size
                }
            }
            
        except Exception as e:
            return {
                "valid": False,
                "issues": [f"音频文件读取失败: {str(e)}"],
                "info": {}
            }


class VoiceprintService:
    """声纹识别服务（占位实现）"""
    
    async def save_audio_file(self, audio_file: UploadFile) -> str:
        """保存音频文件"""
        # TODO: 实现音频文件保存逻辑
        return f"data/voices/{audio_file.filename}"
    
    async def create_voice_profile(self, member_id: int, audio_path: str):
        """创建声纹特征"""
        # TODO: 实现声纹特征提取和保存
        return {"id": 1, "member_id": member_id}
    
    async def update_voice_profile(self, member_id: int, audio_path: str):
        """更新声纹特征"""
        # TODO: 实现声纹特征更新
        return {"id": 1, "member_id": member_id}
    
    async def get_voice_profile(self, member_id: int):
        """获取声纹信息"""
        # TODO: 实现声纹信息获取
        return {"id": 1, "member_id": member_id}
    
    async def delete_voice_profile(self, member_id: int) -> bool:
        """删除声纹"""
        # TODO: 实现声纹删除
        return True
    
    async def identify_speaker(self, audio_path: str) -> Tuple[Optional[FamilyMember], float]:
        """识别说话人"""
        # TODO: 实现声纹识别
        return None, 0.0