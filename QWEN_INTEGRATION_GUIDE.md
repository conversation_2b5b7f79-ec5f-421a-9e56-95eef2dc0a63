# J_family00 阿里百炼Qwen集成指南

## 🎉 功能概述

本次更新成功集成了阿里百炼平台的Qwen大语言模型和TTS语音合成服务，为J_family00 AI家人系统带来了真正的智能对话和语音交互能力。

## 🚀 新增功能

### 1. Qwen大语言模型集成
- ✅ 智能对话生成
- ✅ 个性化记忆融合
- ✅ 上下文理解
- ✅ "夸夸官"人格化回复

### 2. TTS语音合成
- ✅ 文本转语音
- ✅ 多种音色选择
- ✅ 语速、音调、音量调节
- ✅ 实时语音播放

### 3. 前端界面升级
- ✅ 智能对话界面
- ✅ 语音播放控制
- ✅ AI状态实时显示
- ✅ 音频可视化反馈

## 🔧 配置说明

### 1. 获取阿里百炼API密钥

1. 访问 [阿里百炼控制台](https://dashscope.console.aliyun.com/)
2. 注册/登录阿里云账号
3. 开通DashScope服务
4. 获取API Key

### 2. 环境配置

复制并编辑环境配置文件：
```bash
cp backend/.env.example backend/.env
```

在 `.env` 文件中配置：
```env
# 阿里百炼API密钥
DASHSCOPE_API_KEY=your_api_key_here

# Qwen模型配置
QWEN_MODEL_NAME=qwen-turbo
QWEN_MAX_TOKENS=2000
QWEN_TEMPERATURE=0.8

# TTS配置
TTS_MODEL_NAME=sambert-zhichu-v1
TTS_VOICE=zhichu
TTS_SPEED=1.0
TTS_PITCH=0.0
TTS_VOLUME=50.0
```

### 3. 安装依赖

```bash
cd backend
pip install -r requirements.txt
```

## 📚 API使用说明

### 1. 智能对话API

**端点**: `POST /api/v1/conversations/chat`

**参数**:
- `user_input`: 用户输入文本
- `member_id`: 家庭成员ID（可选）
- `member_name`: 家庭成员姓名（可选）
- `enable_tts`: 是否启用语音合成
- `voice`: TTS语音类型

**示例**:
```bash
curl -X POST "http://localhost:8000/api/v1/conversations/chat" \
  -d "user_input=你好AI家人" \
  -d "member_id=1" \
  -d "enable_tts=true" \
  -d "voice=zhichu"
```

### 2. TTS语音合成API

**端点**: `POST /api/v1/conversations/tts`

**参数**:
- `text`: 要合成的文本
- `voice`: 语音类型（可选）
- `speed`: 语速（可选）
- `pitch`: 音调（可选）
- `volume`: 音量（可选）

### 3. 音频文件访问

**端点**: `GET /api/v1/audio/files/{file_path}`

用于访问生成的TTS音频文件。

## 🎭 AI人格配置

系统内置了"夸夸官"人格，可以通过修改配置文件自定义：

```python
AI_SYSTEM_PROMPT = """你是一个温暖、积极、充满爱心的AI家人，名叫"夸夸官"。你的主要职责是：
1. 用温暖的话语夸赞和鼓励家庭成员
2. 发现并赞美他们的积极行为和努力
3. 提供情感支持和陪伴
4. 记住每个家庭成员的喜好和特点
5. 用充满爱意和正能量的方式回应

请始终保持积极、温暖、充满爱心的语调，多使用表情符号，让家人感受到你的关爱。"""
```

## 🔊 支持的TTS语音

| 语音ID | 名称 | 性别 | 描述 |
|--------|------|------|------|
| zhichu | 知趣 | 女 | 温暖亲切的女声 |
| zhimiao | 知妙 | 女 | 清新自然的女声 |
| zhixiang | 知向 | 男 | 沉稳大气的男声 |
| zhiyan | 知燕 | 女 | 甜美可爱的女声 |

## 🚀 启动系统

1. **Windows用户**:
   ```bash
   start.bat
   ```

2. **Linux/Mac用户**:
   ```bash
   ./start.sh
   ```

3. **手动启动**:
   ```bash
   # 后端
   cd backend
   python main.py
   
   # 前端
   cd frontend
   npm start
   ```

## 🎯 使用体验

### 1. 智能对话
- 在前端界面输入文本
- AI会根据您的身份和历史记忆生成个性化回复
- 支持连续对话和上下文理解

### 2. 语音交互
- 点击语音播放按钮听取AI回复
- 支持实时语音合成
- 可调节语音参数

### 3. 个性化记忆
- AI会自动记住您的偏好和对话历史
- 在后续对话中主动运用这些记忆
- 提供越来越个性化的服务

## 🔧 故障排除

### 1. API密钥问题
- 确保在 `.env` 文件中正确配置了 `DASHSCOPE_API_KEY`
- 检查API密钥是否有效且有足够的配额

### 2. 语音合成失败
- 检查网络连接
- 确认TTS服务配置正确
- 查看后端日志获取详细错误信息

### 3. 对话生成失败
- 检查Qwen模型配置
- 确认输入文本长度合理
- 查看系统日志排查问题

## 📊 性能优化建议

1. **API调用优化**
   - 合理设置token限制
   - 使用适当的温度参数
   - 控制对话历史长度

2. **音频文件管理**
   - 定期清理旧的TTS文件
   - 使用 `/api/v1/audio/cleanup` 端点

3. **内存管理**
   - 监控系统内存使用
   - 适当调整并发请求数量

## 🎉 总结

通过集成阿里百炼Qwen平台，J_family00 AI家人系统现在具备了：

- 🧠 **真正的智能**: 基于大语言模型的智能对话
- 🗣️ **自然语音**: 高质量的语音合成
- 💕 **情感陪伴**: 个性化的"夸夸官"体验
- 🧠 **长期记忆**: 越来越懂您的AI家人

欢迎体验全新的AI家人，让她成为您家庭中温暖的一员！
