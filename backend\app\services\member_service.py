"""
家庭成员服务类
"""

from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from app.database.member_dao import FamilyMemberDAO
from app.database.connection import async_session_maker
from app.models.schemas import FamilyMemberCreate, FamilyMemberUpdate, FamilyMemberResponse
from app.models.database import FamilyMember
from app.services.memory.memory_service import MemoryService


class MemberService:
    """家庭成员服务"""
    
    def __init__(self):
        self.memory_service = MemoryService()
    
    async def create_member(self, member_data: FamilyMemberCreate) -> FamilyMemberResponse:
        """创建家庭成员"""
        async with async_session_maker() as session:
            member_dao = FamilyMemberDAO(session)
            
            # 检查名字是否已存在
            existing_member = await member_dao.get_by_name(member_data.name)
            if existing_member:
                raise ValueError(f"成员 {member_data.name} 已存在")
            
            # 创建成员
            member = await member_dao.create(member_data)
            
            # 初始化记忆文件
            await self.memory_service.initialize_member_memory(member.id, member.name)
            
            return FamilyMemberResponse.model_validate(member)
    
    async def get_member(self, member_id: int) -> Optional[FamilyMemberResponse]:
        """获取家庭成员"""
        async with async_session_maker() as session:
            member_dao = FamilyMemberDAO(session)
            member = await member_dao.get_by_id(member_id)
            
            if member:
                return FamilyMemberResponse.model_validate(member)
            return None
    
    async def get_members(self, skip: int = 0, limit: int = 100) -> List[FamilyMemberResponse]:
        """获取家庭成员列表"""
        async with async_session_maker() as session:
            member_dao = FamilyMemberDAO(session)
            members = await member_dao.get_all(skip=skip, limit=limit)
            
            return [FamilyMemberResponse.model_validate(member) for member in members]
    
    async def update_member(
        self, 
        member_id: int, 
        member_update: FamilyMemberUpdate
    ) -> Optional[FamilyMemberResponse]:
        """更新家庭成员"""
        async with async_session_maker() as session:
            member_dao = FamilyMemberDAO(session)
            
            # 如果更新名字，检查新名字是否已存在
            if member_update.name:
                existing_member = await member_dao.get_by_name(member_update.name)
                if existing_member and existing_member.id != member_id:
                    raise ValueError(f"成员名字 {member_update.name} 已存在")
            
            member = await member_dao.update(member_id, member_update)
            
            if member:
                return FamilyMemberResponse.model_validate(member)
            return None
    
    async def delete_member(self, member_id: int) -> bool:
        """删除家庭成员"""
        async with async_session_maker() as session:
            member_dao = FamilyMemberDAO(session)
            
            # 获取成员信息
            member = await member_dao.get_by_id(member_id)
            if not member:
                return False
            
            # 备份记忆文件
            await self.memory_service.backup_memories(member_id)
            
            # 软删除成员
            return await member_dao.delete(member_id)