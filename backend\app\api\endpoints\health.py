"""
健康检查端点
"""

from fastapi import APIRouter, Depends
from typing import Dict
import psutil
import datetime
from app.core.config import settings

router = APIRouter()


@router.get("/system")
async def get_system_status() -> Dict:
    """获取系统状态"""
    try:
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        return {
            "status": "healthy",
            "timestamp": datetime.datetime.now().isoformat(),
            "system": {
                "cpu_usage": f"{cpu_percent}%",
                "memory_usage": f"{memory.percent}%",
                "disk_usage": f"{disk.percent}%",
                "available_memory": f"{memory.available / 1024**3:.2f}GB"
            },
            "config": {
                "project_name": settings.PROJECT_NAME,
                "version": settings.VERSION,
                "debug": settings.DEBUG
            }
        }
    except Exception as e:
        return {
            "status": "error",
            "message": str(e),
            "timestamp": datetime.datetime.now().isoformat()
        }