"""
SQLAlchemy 数据库模型
"""

from sqlalchemy import Column, Integer, String, Text, DateTime, Float, ForeignKey, Boolean
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime

Base = declarative_base()


class FamilyMember(Base):
    """家庭成员模型"""
    
    __tablename__ = "family_members"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(50), nullable=False)
    nickname = Column(String(50))
    voice_profile_path = Column(Text)  # 声纹文件路径
    memory_file_path = Column(Text)    # JSON记忆文件路径
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    conversations = relationship("Conversation", back_populates="member")
    voice_profiles = relationship("VoiceProfile", back_populates="member")


class Conversation(Base):
    """对话记录模型"""
    
    __tablename__ = "conversations"
    
    id = Column(Integer, primary_key=True, index=True)
    member_id = Column(Integer, ForeignKey("family_members.id"))
    input_text = Column(Text)          # 识别到的文本
    input_audio_path = Column(Text)    # 输入音频文件路径
    response_text = Column(Text)       # 回复文本
    response_audio_path = Column(Text) # 回复音频文件路径
    confidence_score = Column(Float)   # 识别置信度
    session_id = Column(String(100))   # 会话ID
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # 关系
    member = relationship("FamilyMember", back_populates="conversations")


class VoiceProfile(Base):
    """声纹特征模型"""
    
    __tablename__ = "voice_profiles"
    
    id = Column(Integer, primary_key=True, index=True)
    member_id = Column(Integer, ForeignKey("family_members.id"))
    profile_data = Column(Text)        # 声纹特征数据（JSON格式存储）
    model_version = Column(String(20)) # 模型版本
    sample_count = Column(Integer, default=0)  # 样本数量
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    member = relationship("FamilyMember", back_populates="voice_profiles")


class AudioFile(Base):
    """音频文件记录模型"""
    
    __tablename__ = "audio_files"
    
    id = Column(Integer, primary_key=True, index=True)
    member_id = Column(Integer, ForeignKey("family_members.id"), nullable=True)
    file_path = Column(Text, nullable=False)
    file_name = Column(String(255))
    file_size = Column(Integer)        # 文件大小（字节）
    duration = Column(Float)           # 音频时长（秒）
    sample_rate = Column(Integer)      # 采样率
    channels = Column(Integer)         # 声道数
    format = Column(String(10))        # 音频格式
    file_type = Column(String(20))     # 文件类型：input, output, voice_sample
    created_at = Column(DateTime, default=datetime.utcnow)


class SystemLog(Base):
    """系统日志模型"""
    
    __tablename__ = "system_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    level = Column(String(10))         # 日志级别：DEBUG, INFO, WARNING, ERROR
    module = Column(String(50))        # 模块名
    message = Column(Text)             # 日志消息
    member_id = Column(Integer, ForeignKey("family_members.id"), nullable=True)
    session_id = Column(String(100))   # 会话ID
    created_at = Column(DateTime, default=datetime.utcnow)