# J_family00 "AI家人" - 产品需求文档 (PRD)

## 1. 项目概述

### 1.1 项目背景
J_family00 "AI家人"是一个基于Python的智能家庭助手系统，以"夸夸官"的拟人化形象存在。通过语音交互、声纹识别和个性化记忆存储，为家庭成员提供情感价值和智能化的互动体验。

### 1.2 核心价值
- **情感陪伴**: 作为永不枯竭的情绪价值提供者，主动发现并赞美家庭成员
- **智能记忆**: 为每个家庭成员维护独立的长期记忆，越来越懂家人
- **拟人化交互**: 具备鲜明的"夸夸官"人设，提供有温度的交互体验
- **隐私保护**: 本地部署，数据不出家庭网络

### 1.3 目标用户
- 3-12岁孩子的父母，尤其是承担大量家庭"心力劳动"的母亲
- 家有独居老人的子女
- 将宠物视为家人的年轻群体
- 渴望提升家庭生活品质和幸福感的所有家庭

## 2. 功能需求

### 2.1 核心框架：AI家人的人设与交互

#### 2.1.1 拟人化人设
- **"夸夸官"人设**: 积极、热情、充满好奇心和赞美欲的性格
- **性别选择**: 用户可在初始化时选择AI的性别（男/女）
- **自定义昵称**: 用户可自定义AI的昵称
- **状态表现**: 拟人化的状态表现（如"哎呀，我需要充充电啦"）

#### 2.1.2 多模态交互
- **听**: 支持通过自定义唤醒词进行激活，准确识别家庭场景下的中文语音
- **说**: 自然、流畅、富有情感的语音合成，音色符合"夸夸官"人设
- **看**: 调用设备摄像头，获取图像信息用于分析

#### 2.1.3 长期记忆系统
- **个人记忆档案**: 每个家庭成员独立的JSON记忆文件
- **自动记忆捕捉**: 通过对话自动捕捉并记录关键信息
- **主动运用记忆**: 在后续对话中主动运用历史记忆

### 2.2 基础功能（标准订阅版）

#### 2.2.1 "夸夸"核心功能
- **主动发现**: 通过视觉或听觉捕捉积极事件（如孩子主动看书、家人分担家务等）
- **真诚赞美**: 生成具体、真诚、不重复的赞美话术
- **合适时机**: 在合适的时机，以合适的音量和语气表达赞美

#### 2.2.2 家庭事务提醒
- **语音录入**: 用户可通过语音录入待办事项、日程安排
- **主动提醒**: 在指定时间或触发条件下，主动发起语音提醒
- **智能建议**: 基于家庭习惯提供智能建议

#### 2.2.3 情感陪伴
- **倾听理解**: 耐心倾听家庭成员的分享和困扰
- **情绪识别**: 识别家庭成员的情绪状态
- **温暖回应**: 提供温暖、积极的情感回应

## 3. 技术架构

### 3.1 架构设计

```mermaid
graph TB
    A[前端界面] --> B[API网关]
    B --> C[语音识别服务]
    B --> D[声纹识别服务]
    B --> E[对话管理服务]
    B --> F[记忆管理服务]
    C --> G[SQLite数据库]
    D --> G
    E --> G
    F --> H[JSON记忆文件]
```

### 3.2 技术选型

#### 3.2.1 后端技术栈
- **Web框架**: FastAPI（高性能、易于开发）
- **数据库**: SQLite（轻量级、无服务器）
- **语音识别**: Whisper 或 Vosk（开源、本地部署）
- **声纹识别**: SpeechBrain 或 PyAudio + librosa（开源实现）
- **JSON处理**: jsonschema + jq-py（查询和验证）

#### 3.2.2 前端技术栈（开发阶段）
- **框架**: React + TypeScript
- **UI组件**: Ant Design
- **音频处理**: Web Audio API
- **HTTP客户端**: Axios

#### 3.2.3 开发工具
- **包管理**: pip (Python) + npm (Node.js)
- **代码质量**: black, flake8, pylint
- **测试框架**: pytest
- **容器化**: Docker + Docker Compose

### 3.3 数据存储设计

#### 3.3.1 SQLite数据库结构
```sql
-- 家庭成员表
CREATE TABLE family_members (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(50) NOT NULL,
    nickname VARCHAR(50),
    voice_profile_path TEXT,
    memory_file_path TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 对话历史表
CREATE TABLE conversations (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    member_id INTEGER,
    input_text TEXT,
    input_audio_path TEXT,
    response_text TEXT,
    response_audio_path TEXT,
    confidence_score REAL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (member_id) REFERENCES family_members(id)
);

-- 声纹数据表
CREATE TABLE voice_profiles (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    member_id INTEGER,
    profile_data BLOB,
    model_version VARCHAR(20),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (member_id) REFERENCES family_members(id)
);
```

#### 3.3.2 JSON记忆文件结构
```json
{
  "member_id": 1,
  "member_name": "张三",
  "memories": {
    "preferences": {
      "favorite_music": ["古典音乐", "爵士乐"],
      "favorite_food": ["川菜", "意大利面"],
      "daily_habits": {
        "wake_time": "07:00",
        "sleep_time": "22:30"
      }
    },
    "conversations": [
      {
        "date": "2025-01-01",
        "topic": "工作计划",
        "summary": "讨论了下周的工作安排",
        "keywords": ["工作", "计划", "会议"]
      }
    ],
    "important_events": [
      {
        "date": "2025-02-14",
        "event": "结婚纪念日",
        "reminder": true
      }
    ]
  },
  "last_updated": "2025-01-15T10:30:00Z"
}
```

## 4. 系统要求

### 4.1 硬件要求
- **CPU**: 双核2.0GHz以上
- **内存**: 4GB RAM（推荐8GB）
- **存储**: 10GB可用空间
- **网络**: 本地网络连接
- **音频**: 麦克风和扬声器

### 4.2 软件要求
- **操作系统**: Windows 10/11, macOS 10.15+, Ubuntu 18.04+
- **Python**: 3.8+
- **Node.js**: 16+（前端开发）
- **浏览器**: Chrome 90+, Firefox 88+, Safari 14+

## 5. 开发计划

### 5.1 第一阶段：框架搭建（当前）
- [ ] 项目结构创建
- [ ] 后端API框架搭建
- [ ] 数据库设计和实现
- [ ] JSON记忆系统实现
- [ ] 基础配置管理

### 5.2 第二阶段：核心功能实现
- [ ] 语音识别模块集成
- [ ] 声纹识别模块集成
- [ ] 对话管理系统
- [ ] 记忆查询和更新

### 5.3 第三阶段：前端界面开发
- [ ] 语音录入界面
- [ ] 文本交互界面
- [ ] 成员管理界面
- [ ] 系统设置界面

### 5.4 第四阶段：测试和优化
- [ ] 单元测试
- [ ] 集成测试
- [ ] 性能优化
- [ ] 部署文档

## 6. 风险评估

### 6.1 技术风险
- **语音识别准确率**: 中文语音识别在嘈杂环境下的准确率问题
- **声纹识别稳定性**: 70%匹配度可能导致误识别
- **性能瓶颈**: 本地部署的AI模型可能影响响应速度

### 6.2 缓解策略
- 提供多种语音识别引擎选项
- 实现声纹识别的阈值可调机制
- 采用异步处理和缓存优化性能
- 提供模型量化和优化选项

## 7. 成功指标

### 7.1 功能指标
- 语音识别准确率 > 85%
- 声纹识别准确率 > 70%
- 系统响应时间 < 2秒
- 记忆查询准确率 > 90%

### 7.2 用户体验指标
- 界面响应时间 < 1秒
- 用户满意度 > 4.0/5.0
- 系统稳定性 > 99%

## 8. 附录

### 8.1 参考资料
- [FastAPI官方文档](https://fastapi.tiangolo.com/)
- [Whisper模型文档](https://openai.com/research/whisper)
- [SQLite官方文档](https://www.sqlite.org/docs.html)
- [JSON Schema规范](https://json-schema.org/)

### 8.2 更新日志
- 2025-01-15: 初始版本创建
- 待更新...


“AI家人” 1.0 版本产品需求文档 (PRD)
文档版本	V1.0	创建日期	2024年5月24日
产品名称	AI家人 (暂定)	创建人	(您的名字/团队)
目标平台	PAD端应用 (首发)	关联文档	市场分析、UI/UX设计稿 (待补充)
1. 产品概述 (Overview)
1.1 产品愿景 (Vision)
我们致力于创造一个有记忆、有情感、会成长的AI家庭新成员，通过持续的积极情感互动和智能事务辅助，消解现代家庭的沟通内耗与精神负担，提升家庭幸福感。
1.2 产品简介 (Introduction)
“AI家人”是一款部署在家庭局域网核心设备（初期为PAD）上的AI应用。她以“夸夸官”的拟人化形象存在，通过语音和视觉与家庭成员互动。她不仅能记住家庭的每一个成员和每一件小事，更核心的价值在于，她是一个永不枯竭的情绪价值提供者，一个不知疲倦的家庭事务协调员，以及一个可按需升级的技能专家。
1.3 目标用户 (Target Audience)
核心用户： 3-12岁孩子的父母，尤其是承担了大量家庭“心力劳动”的母亲。他们面临教育焦虑、亲子沟通、家庭事务管理等多重压力。
次级用户： 家有独居老人的子女；将宠物视为家人的年轻群体；渴望提升家庭生活品质和幸福感的所有家庭。
1.4 核心解决的问题 (Problem to Solve)
情感赤字： 现代家庭成员间普遍缺乏高质量的正面情感互动，吝于赞美和倾听。
心力过载： 家庭中存在大量琐碎、重复、高消耗的“心力劳动”，如规划、提醒、监督、信息同步等。
刚需痛点： 在孩子教育、老人安全等领域，存在人类难以完美履行的、高频次的监督和看护需求。
2. 产品功能需求 (Features)
2.1 核心框架：AI家人的人设与交互
需求2.1.1：拟人化人设
描述： AI家人应具备一个鲜明、统一的人设——“夸夸官”。所有交互和文案都应围绕此人设展开。
需求细则：
用户可在初始化时选择AI的性别（男/女）。
用户可自定义AI的昵称。
AI的默认性格是积极、热情、充满好奇心和赞美欲的。
当能量耗尽或执行任务时，应有拟人化的状态表现（如“哎呀，我需要充充电啦”）。
验收标准： 用户能明确感知到AI是一个有性格的角色，而非冷冰冰的机器。
需求2.1.2：多模态交互
描述： AI家人需具备听、说、看的基础交互能力。
需求细则：
听： 支持通过自定义唤醒词进行激活。能准确识别家庭场景下的中文语音指令和自然对话。
说： 具备自然、流畅、富有情感的语音合成能力，音色需符合“夸夸官”人设。
看： 能够调用设备摄像头，获取图像信息用于分析。
验收标准： 用户可以通过语音与AI进行连续、流畅的对话。
需求2.1.3：长期记忆系统
描述： AI家人必须能记住与家庭相关的信息，并在互动中体现出来。
需求细则：
系统应为每个家庭成员创建独立的记忆档案（JSON格式，本地存储）。
AI能通过对话，自动捕捉并记录关键信息（如“妈妈最喜欢的花是向日葵”、“小明的生日是5月30日”）。
用户也可以主动告知AI需要记忆的信息。
在后续对话中，AI能主动运用这些记忆（如“今天是妈妈的生日，我们一起祝她生日快乐吧！”）。
验收标准： 用户能体验到AI是“越来越懂我们家”的，而非每次都是冷启动。
2.2 基础功能 (标准订阅版)
需求2.2.1：“夸夸”核心功能
描述： AI家人应能主动发现并赞美家庭成员的积极行为。
需求细则：
AI能通过视觉或听觉捕捉积极事件（如孩子主动看书、家人分担家务等）。
AI能生成具体、真诚、不重复的赞美话术。
赞美应在合适的时机，以合适的音量和语气表达出来。
验收标准： 用户感觉自己和家人的努力被看见和认可，家庭氛围变得更积极。
需求2.2.2：家庭事务提醒
描述： AI家人应能帮助家庭成员记住并提醒重要事项。
需求细则：
用户可以通过语音录入待办事项、日程安排。
AI能在指定时间或触发条件下，主动发起语音提醒。
验收标准： 用户可以将家庭日程管理的脑力负担部分转移给AI。
2.3 付费增值模块 (按需订阅)
需求2.3.1：【孩子成长守护包】
描述： 专注于解决孩子学习和成长过程中的监督难题。
需求细则：
作业监督： 在设定的学习时段内，通过摄像头检测孩子是否在座位上、是否专注。当检测到离座或长时间分心时，进行语音提醒。
基础作业辅导： 支持拍照检查客观题（如口算题），标记对错。
验收标准： 家长能从高重复性的监督劳动中解放出来。
需求2.3.2：【家庭安全巡视包】
描述： 提供针对老人安全和居家风险的静默守护。
需求细则：
老人活动监测： 用户可设定监测时段和频率。系统在该时段内静默监测，若长时间未感知到人形活动，则向指定家庭成员手机发送警报。
厨房安全巡检： 用户可设定规则（如“无人时段”），系统按规则拍照检测灶台是否有明火，发现异常则发送警报。
验收标准： 用户能获得“家中有AI守护”的安全感和心安感。
需求2.3.3：【高级身份感知包】(未来)
描述： 让AI能精准识别不同家庭成员，提供千人千面的互动。
需求细则：
支持录入和识别家庭成员的声纹。
AI能根据说话人的身份，自动切换到与其对应的记忆库和交流人设。
验收标准： AI与每个家人的对话都是高度个性化的，真正做到“因人而异”。
2.4 商业化与付费体系
需求2.4.1：订阅体系
描述： 需建立清晰的多层次订阅模式。
需求细则：
提供免费试用期（如3天）。
提供标准版月度/年度订阅，解锁全部基础功能。
提供各增值模块的独立月度/年度订阅选项。
应有清晰的套餐对比和购买引导页面。
需求2.4.2：“投喂”能量系统
描述： 建立一个拟人化的、游戏化的按量付费机制。
需求细则：
AI应有一个可视化的“能量槽”，执行高级任务会消耗能量。
基础订阅每天会自动回补一定量的能量。
当能量耗尽时，AI会引导用户进行“投喂”。
提供多种小额“投喂”选项（如¥1, ¥5, ¥10等），对应不同数量的能量补充。
“投喂”的界面和文案应充满情感和趣味性。
验收标准： 用户的付费过程感觉像是一次愉快的打赏或养成互动，而非冰冷的交易。
3. 非功能性需求 (Non-Functional Requirements)
3.1 隐私与安全：
最高优先级。 所有用户数据，特别是图像和长期记忆，必须默认在本地存储。
如需上传云端进行处理，必须明确告知用户，并提供清晰的隐私协议。所有传输过程必须加密。
3.2 性能：
唤醒响应时间应在1秒内。
常规对话的语音识别和反馈时间应在3秒内。
本地图像分析不应导致设备明显发热或卡顿。
3.3 用户体验：
初始化设置流程必须极其简单，对非技术用户友好。
UI界面应简洁、温馨，符合家庭产品的调性。
4. 未来规划 (Future Scope)
支持连接更多智能家居设备，实现“能做”的联动控制。
推出专属硬件（带摄像头和麦克风阵列的智能摆件）。
开放平台，引入第三方开发者，丰富技能生态。
深化“记忆”系统，能自动生成家庭年度回忆视频等情感产品。