"""
系统初始化管理
"""

import os
import asyncio
from pathlib import Path
from app.core.config import settings
from app.database.connection import db_manager
from app.core.config_manager import config_manager


class SystemInitializer:
    """系统初始化器"""
    
    def __init__(self):
        self.initialized = False
    
    async def initialize(self):
        """初始化系统"""
        if self.initialized:
            return
        
        print("正在初始化 J_family00 智能家庭助手...")
        
        # 1. 创建必要的目录
        await self._create_directories()
        
        # 2. 初始化数据库
        await self._init_database()
        
        # 3. 验证配置
        await self._validate_configuration()
        
        # 4. 初始化AI模型（如果需要）
        await self._init_models()
        
        # 5. 设置日志
        await self._setup_logging()
        
        self.initialized = True
        print("系统初始化完成！")
    
    async def _create_directories(self):
        """创建必要的目录"""
        directories = [
            settings.DATA_DIR,
            settings.MEMORIES_DIR,
            settings.AUDIO_DIR,
            settings.VOICES_DIR,
            settings.MODELS_DIR,
            settings.LOGS_DIR,
            f"{settings.MEMORIES_DIR}/backups",
            f"{settings.AUDIO_DIR}/temp",
            f"{settings.VOICES_DIR}/profiles",
            f"{settings.MODELS_DIR}/cache"
        ]
        
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
        
        print("✓ 目录结构创建完成")
    
    async def _init_database(self):
        """初始化数据库"""
        try:
            await db_manager.init_db()
            print("✓ 数据库初始化完成")
        except Exception as e:
            print(f"✗ 数据库初始化失败: {e}")
            raise
    
    async def _validate_configuration(self):
        """验证配置"""
        validation_result = config_manager.validate_config()
        
        if validation_result["valid"]:
            print("✓ 配置验证通过")
        else:
            print("⚠ 配置验证发现问题:")
            for issue in validation_result["issues"]:
                print(f"  - {issue}")
    
    async def _init_models(self):
        """初始化AI模型"""
        # 检查语音识别模型
        await self._check_speech_model()
        
        # 检查声纹识别模型
        await self._check_voice_model()
        
        print("✓ AI模型检查完成")
    
    async def _check_speech_model(self):
        """检查语音识别模型"""
        model_type = config_manager.get('speech_recognition.model', 'whisper')
        
        if model_type == 'whisper':
            # Whisper模型会在首次使用时自动下载
            print("  - Whisper模型将在首次使用时自动下载")
        
        elif model_type == 'vosk':
            model_path = Path(settings.VOSK_MODEL_PATH)
            if not model_path.exists():
                print(f"  ⚠ Vosk模型不存在: {model_path}")
                print("    请下载Vosk中文模型到指定路径")
            else:
                print("  - Vosk模型检查通过")
    
    async def _check_voice_model(self):
        """检查声纹识别模型"""
        # 声纹识别使用自定义实现，无需预训练模型
        print("  - 声纹识别模块就绪")
    
    async def _setup_logging(self):
        """设置日志系统"""
        log_file = Path(settings.LOGS_DIR) / "j_family00.log"
        
        # 这里可以集成loguru或其他日志库
        print(f"✓ 日志系统设置完成: {log_file}")
    
    async def cleanup(self):
        """系统清理"""
        if not self.initialized:
            return
        
        print("正在清理系统资源...")
        
        # 关闭数据库连接
        await db_manager.close()
        
        print("系统清理完成")


# 全局系统初始化器
system_initializer = SystemInitializer()


async def startup_event():
    """应用启动事件"""
    await system_initializer.initialize()


async def shutdown_event():
    """应用关闭事件"""
    await system_initializer.cleanup()