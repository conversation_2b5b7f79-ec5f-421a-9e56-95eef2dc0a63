import React, { useState, useEffect } from 'react';
import { Layout, Card, Button, Input, List, message, Upload, Space, Row, Col } from 'antd';
import { AudioOutlined, UserOutlined, MessageOutlined, UploadOutlined, HeartOutlined, SoundOutlined, PauseOutlined } from '@ant-design/icons';
import axios from 'axios';
import AIPersonality from './components/AIPersonality';
import VoiceRecorder from './components/VoiceRecorder';
import RealTimeVoiceInput from './components/RealTimeVoiceInput';
import './App.css';

const { Header, Content, Sider } = Layout;
const { TextArea } = Input;

interface Member {
  id: number;
  name: string;
  nickname?: string;
  is_active: boolean;
  created_at: string;
}

interface Conversation {
  id: number;
  member_id?: number;
  input_text?: string;
  response_text?: string;
  created_at: string;
}

const App: React.FC = () => {
  const [members, setMembers] = useState<Member[]>([]);
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [selectedMember, setSelectedMember] = useState<number | null>(null);
  const [inputText, setInputText] = useState('');
  const [loading, setLoading] = useState(false);
  const [aiEnergy, setAiEnergy] = useState(85);
  const [aiMood, setAiMood] = useState<'happy' | 'excited' | 'tired' | 'charging'>('happy');
  const [isPlayingAudio, setIsPlayingAudio] = useState(false);
  const [currentAudio, setCurrentAudio] = useState<HTMLAudioElement | null>(null);
  const [autoPlayAudio, setAutoPlayAudio] = useState(true); // 自动播放语音

  // 获取家庭成员列表
  const fetchMembers = async () => {
    try {
      const response = await axios.get('/api/v1/members/');
      setMembers(response.data);
    } catch (error) {
      message.error('获取家庭成员失败');
    }
  };

  // 获取对话记录
  const fetchConversations = async () => {
    try {
      const response = await axios.get('/api/v1/conversations/');
      setConversations(response.data);
    } catch (error) {
      message.error('获取对话记录失败');
    }
  };

  // 添加家庭成员
  const addMember = async (name: string, nickname?: string) => {
    try {
      await axios.post('/api/v1/members/', { name, nickname });
      message.success('添加成员成功');
      fetchMembers();
    } catch (error) {
      message.error('添加成员失败');
    }
  };

  // 发送消息
  const sendMessage = async () => {
    if (!inputText.trim()) return;

    setLoading(true);
    try {
      // 使用新的AI对话API，默认启用语音合成
      const response = await axios.post('/api/v1/conversations/chat', null, {
        params: {
          user_input: inputText,
          member_id: selectedMember,
          member_name: selectedMember ? members.find(m => m.id === selectedMember)?.name : undefined,
          enable_tts: autoPlayAudio,  // 根据设置决定是否启用语音合成
          voice: 'zhichu'    // 使用知趣语音
        }
      });

      if (response.data.success) {
        message.success('AI家人回复了您！');

        // 自动播放语音（如果启用了自动播放且有音频）
        if (autoPlayAudio && response.data.audio && response.data.audio.audio_url) {
          playAudio(response.data.audio.audio_url);
        } else if (!autoPlayAudio) {
          // 如果没有启用自动播放，显示简单的心情变化
          setAiMood('excited');
          setTimeout(() => setAiMood('happy'), 2000);
        }

        // 消耗AI能量
        setAiEnergy(prev => Math.max(0, prev - 3));

      } else {
        message.warning('AI家人回复了，但可能有些问题');
      }

      setInputText('');
      fetchConversations();

    } catch (error) {
      console.error('发送消息失败:', error);
      message.error('发送消息失败，请检查网络连接');
    } finally {
      setLoading(false);
    }
  };

  // 处理语音录制完成
  const handleVoiceRecording = async (audioBlob: Blob, duration: number) => {
    const formData = new FormData();
    formData.append('audio_file', audioBlob, 'recording.webm');

    try {
      setLoading(true);
      const response = await axios.post('/api/v1/voice/recognize', formData, {
        headers: { 'Content-Type': 'multipart/form-data' }
      });

      if (response.data.success) {
        setInputText(response.data.text);
        message.success('语音识别成功！');

        // 消耗AI能量
        setAiEnergy(prev => Math.max(0, prev - 2));
      } else {
        message.error('语音识别失败：' + response.data.error);
      }
    } catch (error) {
      message.error('语音识别失败');
    } finally {
      setLoading(false);
    }
  };

  // 上传音频文件
  const uploadAudio = async (file: File) => {
    const formData = new FormData();
    formData.append('audio_file', file);

    try {
      const response = await axios.post('/api/v1/voice/recognize', formData, {
        headers: { 'Content-Type': 'multipart/form-data' }
      });

      message.success('语音识别成功');
      setInputText(response.data.text || '');
    } catch (error) {
      message.error('语音识别失败');
    }

    return false; // 阻止默认上传行为
  };

  // AI能量充值
  const handleEnergyRecharge = () => {
    setAiEnergy(prev => Math.min(100, prev + 15));
    setAiMood('charging');
    message.success('感谢您的投喂！AI家人充满了能量！💕');

    setTimeout(() => {
      setAiMood('excited');
      setTimeout(() => setAiMood('happy'), 2000);
    }, 1000);
  };

  // AI设置
  const handleAISettings = () => {
    message.info('AI设置功能开发中...');
  };

  // 播放音频
  const playAudio = (audioUrl: string) => {
    try {
      // 停止当前播放的音频
      if (currentAudio) {
        currentAudio.pause();
        currentAudio.currentTime = 0;
      }

      const audio = new Audio(audioUrl);
      setCurrentAudio(audio);
      setIsPlayingAudio(true);

      // AI开始说话时的心情变化
      setAiMood('excited');

      audio.onended = () => {
        setIsPlayingAudio(false);
        setCurrentAudio(null);
        // 说话结束后恢复心情
        setAiMood('happy');
      };

      audio.onerror = () => {
        setIsPlayingAudio(false);
        setCurrentAudio(null);
        setAiMood('happy');
        message.error('音频播放失败');
      };

      audio.play().catch(error => {
        console.error('音频播放失败:', error);
        setIsPlayingAudio(false);
        setCurrentAudio(null);
        setAiMood('happy');
        message.error('音频播放失败');
      });

    } catch (error) {
      console.error('音频播放错误:', error);
      setIsPlayingAudio(false);
      setAiMood('happy');
      message.error('音频播放失败');
    }
  };

  // 停止音频播放
  const stopAudio = () => {
    if (currentAudio) {
      currentAudio.pause();
      currentAudio.currentTime = 0;
      setCurrentAudio(null);
      setIsPlayingAudio(false);
    }
  };

  // 文本转语音
  const handleTextToSpeech = async (text: string) => {
    if (!text.trim()) return;

    try {
      setIsPlayingAudio(true);

      const response = await axios.post('/api/v1/conversations/tts', null, {
        params: {
          text: text,
          voice: 'zhichu',
          speed: 1.0,
          volume: 50
        }
      });

      if (response.data.success && response.data.audio_url) {
        playAudio(response.data.audio_url);
      } else {
        throw new Error('TTS生成失败');
      }

    } catch (error) {
      console.error('TTS失败:', error);
      message.error('语音合成失败');
      setIsPlayingAudio(false);
    }
  };

  // 处理语音输入
  const handleVoiceInput = (transcript: string) => {
    if (transcript.trim()) {
      setInputText(transcript);
      message.success(`语音识别成功: ${transcript}`);
    }
  };

  useEffect(() => {
    fetchMembers();
    fetchConversations();
  }, []);

  return (
    <Layout style={{ minHeight: '100vh', background: '#f0f2f5' }}>
      <Header style={{
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        color: 'white',
        textAlign: 'center',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
      }}>
        <Space align="center">
          <HeartOutlined style={{ fontSize: '24px', color: '#ff4d4f' }} />
          <h1 style={{ color: 'white', margin: 0 }}>AI家人 - 您的专属夸夸官</h1>
          <HeartOutlined style={{ fontSize: '24px', color: '#ff4d4f' }} />
        </Space>
      </Header>

      <Layout>
        <Sider width={350} style={{ background: 'transparent', padding: '16px' }}>
          <Space direction="vertical" size="large" style={{ width: '100%' }}>
            {/* AI家人人格展示 */}
            <AIPersonality
              nickname={isPlayingAudio ? "正在说话的夸夸官" : "夸夸官"}
              energy={aiEnergy}
              mood={aiMood}
              onEnergyRecharge={handleEnergyRecharge}
              onSettingsClick={handleAISettings}
            />

            {/* 家庭成员管理 */}
            <Card title="👨‍👩‍👧‍👦 家庭成员" size="small">
              <List
                dataSource={members}
                renderItem={(member) => (
                  <List.Item
                    onClick={() => setSelectedMember(member.id)}
                    style={{
                      cursor: 'pointer',
                      background: selectedMember === member.id ? '#e6f7ff' : 'transparent',
                      borderRadius: '6px',
                      padding: '8px',
                      margin: '4px 0'
                    }}
                  >
                    <UserOutlined /> {member.name} {member.nickname && `(${member.nickname})`}
                  </List.Item>
                )}
              />
              <Button
                type="primary"
                onClick={() => {
                  const name = prompt('请输入成员姓名:');
                  if (name) {
                    const nickname = prompt('请输入昵称（可选）:');
                    addMember(name, nickname || undefined);
                  }
                }}
                style={{ width: '100%', marginTop: 8 }}
                size="small"
              >
                添加成员
              </Button>
            </Card>
          </Space>
        </Sider>

        <Content style={{ padding: '16px' }}>
          <Row gutter={16}>
            <Col span={16}>
              <Card
                title="💬 与AI家人对话"
                style={{ marginBottom: 16 }}
                headStyle={{ background: '#fafafa' }}
              >
                <div style={{ height: '400px', overflowY: 'auto', marginBottom: 16 }}>
                  <List
                    dataSource={conversations}
                    renderItem={(conv) => (
                      <List.Item style={{ border: 'none', padding: '8px 0' }}>
                        <div style={{ width: '100%' }}>
                          {/* 用户消息 */}
                          <div style={{
                            textAlign: 'right',
                            marginBottom: '8px',
                            display: 'flex',
                            justifyContent: 'flex-end'
                          }}>
                            <div style={{
                              background: '#1890ff',
                              color: 'white',
                              padding: '8px 12px',
                              borderRadius: '12px',
                              maxWidth: '70%',
                              wordBreak: 'break-word'
                            }}>
                              {conv.input_text}
                            </div>
                          </div>

                          {/* AI回复 */}
                          <div style={{
                            textAlign: 'left',
                            display: 'flex',
                            justifyContent: 'flex-start',
                            alignItems: 'flex-start',
                            gap: '8px'
                          }}>
                            <div
                              className={isPlayingAudio ? 'ai-message-speaking' : ''}
                              style={{
                                background: '#f6ffed',
                                border: '1px solid #b7eb8f',
                                color: '#389e0d',
                                padding: '8px 12px',
                                borderRadius: '12px',
                                maxWidth: '70%',
                                wordBreak: 'break-word',
                                position: 'relative',
                                transition: 'all 0.3s ease'
                              }}
                            >
                              {conv.response_text}

                              {/* 语音状态指示器 */}
                              {isPlayingAudio && (
                                <div
                                  className="voice-indicator"
                                  style={{
                                    position: 'absolute',
                                    top: '-8px',
                                    right: '-8px',
                                    background: '#52c41a',
                                    borderRadius: '50%',
                                    width: '16px',
                                    height: '16px',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center'
                                  }}
                                >
                                  <SoundOutlined style={{ fontSize: '8px', color: 'white' }} />
                                </div>
                              )}
                            </div>
                          </div>

                          <div style={{
                            textAlign: 'center',
                            marginTop: '4px',
                            fontSize: '12px',
                            color: '#999'
                          }}>
                            {new Date(conv.created_at).toLocaleString()}
                          </div>
                        </div>
                      </List.Item>
                    )}
                  />
                </div>

                <Space direction="vertical" style={{ width: '100%' }}>
                  <TextArea
                    value={inputText}
                    onChange={(e) => setInputText(e.target.value)}
                    placeholder="和AI家人说说话吧，她会夸夸您哦~ 💕"
                    autoSize={{ minRows: 2, maxRows: 4 }}
                    style={{ borderRadius: '8px' }}
                  />

                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%', gap: '8px' }}>
                    <div style={{ display: 'flex', gap: '8px' }}>
                      <RealTimeVoiceInput
                        onTranscript={handleVoiceInput}
                        disabled={loading}
                      />

                      <Upload
                        beforeUpload={uploadAudio}
                        accept="audio/*"
                        showUploadList={false}
                      >
                        <Button icon={<UploadOutlined />} size="small">上传</Button>
                      </Upload>
                    </div>

                    <Button
                      type="primary"
                      onClick={sendMessage}
                      loading={loading}
                      disabled={!inputText.trim()}
                      style={{ borderRadius: '6px' }}
                    >
                      发送消息
                    </Button>
                  </div>
                </Space>
              </Card>
            </Col>

            <Col span={8}>
              <Card
                title="🎤 语音录制"
                size="small"
                style={{ marginBottom: 16 }}
              >
                <VoiceRecorder
                  onRecordingComplete={handleVoiceRecording}
                  maxDuration={30}
                  disabled={loading}
                />
              </Card>

              <Card
                title="📊 今日互动统计"
                size="small"
              >
                <Space direction="vertical" style={{ width: '100%' }}>
                  <div>💬 对话次数: {conversations.length}</div>
                  <div>⚡ AI能量: {aiEnergy}%</div>
                  <div>😊 AI心情: {
                    isPlayingAudio ? '正在说话' :
                    aiMood === 'happy' ? '开心' :
                    aiMood === 'excited' ? '兴奋' :
                    aiMood === 'tired' ? '疲惫' : '充电中'
                  }</div>
                  <div>🔊 语音状态: {isPlayingAudio ? '播放中' : '静音'}</div>
                  {selectedMember && (
                    <div>👤 当前对话: {members.find(m => m.id === selectedMember)?.name || '未知'}</div>
                  )}
                </Space>
              </Card>

              <Card
                title="🔧 语音设置"
                size="small"
              >
                <Space direction="vertical" style={{ width: '100%' }}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <span>🎵 自动语音播报</span>
                    <Button
                      type={autoPlayAudio ? "primary" : "default"}
                      size="small"
                      onClick={() => {
                        setAutoPlayAudio(!autoPlayAudio);
                        message.info(autoPlayAudio ? '已关闭自动语音播报' : '已开启自动语音播报');
                      }}
                    >
                      {autoPlayAudio ? '开启' : '关闭'}
                    </Button>
                  </div>

                  {isPlayingAudio && (
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <span>⏸️ 停止播放</span>
                      <Button
                        type="default"
                        size="small"
                        danger
                        onClick={stopAudio}
                      >
                        停止
                      </Button>
                    </div>
                  )}
                </Space>
              </Card>
            </Col>
          </Row>
        </Content>
      </Layout>
    </Layout>
  );
};

export default App;