"""
系统配置API端点
"""

from fastapi import APIRouter, HTTPException, status
from typing import Dict, Any
from app.core.config_manager import config_manager

router = APIRouter()


@router.get("/")
async def get_all_config() -> Dict[str, Any]:
    """获取所有配置"""
    return config_manager.get_all()


@router.get("/{section}")
async def get_config_section(section: str) -> Dict[str, Any]:
    """获取指定配置节"""
    config = config_manager.get(section)
    if config is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"配置节 '{section}' 不存在"
        )
    return {section: config}


@router.get("/{section}/{key}")
async def get_config_value(section: str, key: str) -> Dict[str, Any]:
    """获取指定配置值"""
    config_key = f"{section}.{key}"
    value = config_manager.get(config_key)
    if value is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"配置项 '{config_key}' 不存在"
        )
    return {config_key: value}


@router.put("/{section}/{key}")
async def update_config_value(section: str, key: str, value: Any) -> Dict[str, Any]:
    """更新指定配置值"""
    try:
        config_key = f"{section}.{key}"
        config_manager.set(config_key, value)
        return {"message": f"配置项 '{config_key}' 更新成功", "value": value}
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"更新配置失败: {str(e)}"
        )


@router.put("/")
async def update_multiple_config(updates: Dict[str, Any]) -> Dict[str, str]:
    """批量更新配置"""
    try:
        config_manager.update(updates)
        return {"message": f"成功更新 {len(updates)} 个配置项"}
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"批量更新配置失败: {str(e)}"
        )


@router.post("/reset/{section}")
async def reset_config_section(section: str) -> Dict[str, str]:
    """重置指定配置节为默认值"""
    try:
        config_manager.reset_to_default(section)
        return {"message": f"配置节 '{section}' 已重置为默认值"}
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"重置配置失败: {str(e)}"
        )


@router.post("/reset")
async def reset_all_config() -> Dict[str, str]:
    """重置所有配置为默认值"""
    try:
        config_manager.reset_to_default()
        return {"message": "所有配置已重置为默认值"}
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"重置配置失败: {str(e)}"
        )


@router.get("/validate")
async def validate_config() -> Dict[str, Any]:
    """验证配置有效性"""
    return config_manager.validate_config()


@router.post("/export")
async def export_config(file_path: str) -> Dict[str, str]:
    """导出配置到文件"""
    try:
        config_manager.export_config(file_path)
        return {"message": f"配置已导出到 {file_path}"}
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.post("/import")
async def import_config(file_path: str) -> Dict[str, str]:
    """从文件导入配置"""
    try:
        config_manager.import_config(file_path)
        return {"message": f"配置已从 {file_path} 导入"}
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )