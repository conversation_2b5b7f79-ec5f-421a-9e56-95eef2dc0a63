"""
对话服务类
"""

from typing import List, Optional, Dict, Any
from datetime import datetime
from app.database.conversation_dao import ConversationDA<PERSON>
from app.database.connection import async_session_maker
from app.models.schemas import ConversationCreate, ConversationResponse
from app.services.llm.qwen_service import QwenLLMService
from app.core.logger import get_logger

logger = get_logger("conversation")


class ConversationService:
    """对话服务"""

    def __init__(self):
        self.llm_service = QwenLLMService()

    async def create_conversation(self, conversation_data: ConversationCreate) -> ConversationResponse:
        """创建对话记录"""
        async with async_session_maker() as session:
            conversation_dao = ConversationDAO(session)
            conversation = await conversation_dao.create(conversation_data)
            return ConversationResponse.model_validate(conversation)
    
    async def get_conversation(self, conversation_id: int) -> Optional[ConversationResponse]:
        """获取对话记录"""
        async with async_session_maker() as session:
            conversation_dao = ConversationDAO(session)
            conversation = await conversation_dao.get_by_id(conversation_id)
            
            if conversation:
                return ConversationResponse.model_validate(conversation)
            return None
    
    async def get_conversations(
        self, 
        member_id: Optional[int] = None,
        skip: int = 0, 
        limit: int = 50
    ) -> List[ConversationResponse]:
        """获取对话记录列表"""
        async with async_session_maker() as session:
            conversation_dao = ConversationDAO(session)
            conversations = await conversation_dao.get_all(
                member_id=member_id,
                skip=skip, 
                limit=limit
            )
            
            return [ConversationResponse.model_validate(conv) for conv in conversations]
    
    async def delete_conversation(self, conversation_id: int) -> bool:
        """删除对话记录"""
        async with async_session_maker() as session:
            conversation_dao = ConversationDAO(session)
            return await conversation_dao.delete(conversation_id)

    async def generate_ai_response(
        self,
        user_input: str,
        member_id: Optional[int] = None,
        member_name: Optional[str] = None,
        session_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        生成AI智能回复

        Args:
            user_input: 用户输入
            member_id: 家庭成员ID
            member_name: 家庭成员姓名
            session_id: 会话ID

        Returns:
            包含AI回复和相关信息的字典
        """
        try:
            # 获取对话历史
            conversation_history = []
            if member_id:
                recent_conversations = await self.get_conversations(
                    member_id=member_id,
                    skip=0,
                    limit=10
                )
                conversation_history = [
                    {
                        "input_text": conv.input_text,
                        "response_text": conv.response_text
                    }
                    for conv in recent_conversations
                ]

            # 生成AI回复
            llm_result = await self.llm_service.generate_response(
                user_input=user_input,
                member_id=member_id,
                member_name=member_name,
                conversation_history=conversation_history
            )

            if llm_result["success"]:
                # 保存对话记录
                conversation_data = ConversationCreate(
                    member_id=member_id,
                    input_text=user_input,
                    response_text=llm_result["response"],
                    confidence_score=0.95,  # LLM回复的置信度
                    session_id=session_id
                )

                conversation = await self.create_conversation(conversation_data)

                logger.info(f"AI对话生成成功: {member_name}, 对话ID: {conversation.id}")

                return {
                    "conversation_id": conversation.id,
                    "response": llm_result["response"],
                    "model_info": {
                        "model": llm_result["model"],
                        "usage": llm_result.get("usage", {})
                    },
                    "success": True,
                    "timestamp": llm_result["timestamp"]
                }
            else:
                # LLM生成失败，使用备用回复
                fallback_response = llm_result["response"]

                conversation_data = ConversationCreate(
                    member_id=member_id,
                    input_text=user_input,
                    response_text=fallback_response,
                    confidence_score=0.5,
                    session_id=session_id
                )

                conversation = await self.create_conversation(conversation_data)

                return {
                    "conversation_id": conversation.id,
                    "response": fallback_response,
                    "error": llm_result.get("error"),
                    "success": False,
                    "timestamp": llm_result["timestamp"]
                }

        except Exception as e:
            logger.error(f"AI对话生成失败: {str(e)}")

            # 使用默认回复
            default_response = "抱歉，我现在有点累了，请稍后再试试吧~ 💕"

            try:
                conversation_data = ConversationCreate(
                    member_id=member_id,
                    input_text=user_input,
                    response_text=default_response,
                    confidence_score=0.1,
                    session_id=session_id
                )

                conversation = await self.create_conversation(conversation_data)

                return {
                    "conversation_id": conversation.id,
                    "response": default_response,
                    "error": str(e),
                    "success": False,
                    "timestamp": datetime.utcnow().isoformat()
                }
            except:
                return {
                    "response": default_response,
                    "error": str(e),
                    "success": False,
                    "timestamp": datetime.utcnow().isoformat()
                }