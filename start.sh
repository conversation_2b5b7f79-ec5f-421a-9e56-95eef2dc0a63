#!/bin/bash

echo "====================================="
echo "  J_family00 智能家庭助手启动脚本"
echo "====================================="
echo

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    echo "[错误] 未检测到Python3，请先安装Python 3.8+"
    exit 1
fi

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    echo "[错误] 未检测到Node.js，请先安装Node.js 16+"
    exit 1
fi

# 设置工作目录
cd "$(dirname "$0")"

# 创建虚拟环境（如果不存在）
if [ ! -d "venv" ]; then
    echo "[信息] 创建Python虚拟环境..."
    python3 -m venv venv
fi

# 激活虚拟环境
echo "[信息] 激活Python虚拟环境..."
source venv/bin/activate

# 安装后端依赖
echo "[信息] 安装后端依赖..."
cd backend
pip install -r requirements.txt
if [ $? -ne 0 ]; then
    echo "[错误] 后端依赖安装失败"
    exit 1
fi

# 创建环境配置文件
if [ ! -f ".env" ]; then
    echo "[信息] 创建环境配置文件..."
    cp .env.example .env
    echo "[提示] 请根据需要修改 .env 文件中的配置"
fi

# 启动后端服务（后台运行）
echo "[信息] 启动后端服务..."
nohup python main.py > ../logs/backend.log 2>&1 &
BACKEND_PID=$!
echo "后端服务PID: $BACKEND_PID"

# 等待后端启动
sleep 3

# 安装前端依赖
echo "[信息] 安装前端依赖..."
cd ../frontend
if [ ! -d "node_modules" ]; then
    npm install
    if [ $? -ne 0 ]; then
        echo "[错误] 前端依赖安装失败"
        kill $BACKEND_PID
        exit 1
    fi
fi

# 启动前端服务（后台运行）
echo "[信息] 启动前端服务..."
nohup npm start > ../logs/frontend.log 2>&1 &
FRONTEND_PID=$!
echo "前端服务PID: $FRONTEND_PID"

echo
echo "====================================="
echo "  启动完成！"
echo "  后端服务: http://localhost:8000"
echo "  前端界面: http://localhost:3000"
echo "  API文档: http://localhost:8000/docs"
echo "====================================="
echo
echo "服务进程ID:"
echo "  后端: $BACKEND_PID"
echo "  前端: $FRONTEND_PID"
echo
echo "要停止服务，请运行: ./stop.sh"
echo

# 保存PID到文件
echo $BACKEND_PID > backend.pid
echo $FRONTEND_PID > frontend.pid