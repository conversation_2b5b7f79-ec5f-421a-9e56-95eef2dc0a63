"""
声纹识别服务
基于WeSpeaker引擎
"""

import os
import uuid
import aiofiles
from typing import Optional, Tu<PERSON>, Dict, Any, List
from pathlib import Path
from fastapi import UploadFile
from app.core.config import settings
from app.core.config_manager import config_manager
from app.services.voice.wespeaker_engine import WeSpeakerEngine
from app.models.database import FamilyMember
from app.database.member_dao import FamilyMemberDAO
from app.database.connection import async_session_maker


class VoiceprintService:
    """声纹识别服务"""
    
    def __init__(self):
        self.engine = WeSpeakerEngine()
        self.similarity_threshold = config_manager.get('voice_recognition.similarity_threshold', 0.7)
    
    async def save_audio_file(self, audio_file: UploadFile, member_id: Optional[int] = None) -> str:
        """保存音频文件"""
        try:
            # 生成唯一文件名
            file_extension = audio_file.filename.split('.')[-1] if '.' in audio_file.filename else 'wav'
            unique_filename = f"voice_{uuid.uuid4()}.{file_extension}"
            
            # 构建保存路径
            if member_id:
                save_dir = Path(settings.VOICES_DIR) / f"member_{member_id}"
            else:
                save_dir = Path(settings.VOICES_DIR) / "temp"
            
            save_dir.mkdir(parents=True, exist_ok=True)
            audio_path = save_dir / unique_filename
            
            # 保存文件
            async with aiofiles.open(audio_path, 'wb') as f:
                content = await audio_file.read()
                await f.write(content)
            
            return str(audio_path)
            
        except Exception as e:
            raise RuntimeError(f"保存声纹音频文件失败: {str(e)}")
    
    async def create_voice_profile(
        self, 
        member_id: int, 
        audio_path: str
    ) -> Dict[str, Any]:
        """
        创建声纹特征
        
        Args:
            member_id: 成员ID
            audio_path: 音频文件路径
            
        Returns:
            声纹特征信息
        """
        try:
            # 获取成员信息
            async with async_session_maker() as session:
                member_dao = FamilyMemberDAO(session)
                member = await member_dao.get_by_id(member_id)
                
                if not member:
                    raise ValueError(f"成员ID {member_id} 不存在")
                
                # 注册声纹
                result = await self.engine.register_speaker(
                    member_id=member_id,
                    audio_path=audio_path,
                    member_name=member.name
                )
                
                if result["success"]:
                    # 更新成员表中的声纹文件路径
                    await member_dao.update_voice_profile_path(member_id, audio_path)
                
                return result
                
        except Exception as e:
            print(f"创建声纹特征失败: {e}")
            return {
                "success": False,
                "member_id": member_id,
                "error": str(e),
                "message": "创建声纹特征失败"
            }
    
    async def update_voice_profile(
        self, 
        member_id: int, 
        audio_path: str
    ) -> Dict[str, Any]:
        """
        更新声纹特征
        
        Args:
            member_id: 成员ID
            audio_path: 新的音频文件路径
            
        Returns:
            更新结果
        """
        try:
            result = await self.engine.update_speaker(member_id, audio_path)
            return result
            
        except Exception as e:
            print(f"更新声纹特征失败: {e}")
            return {
                "success": False,
                "member_id": member_id,
                "error": str(e),
                "message": "更新声纹特征失败"
            }
    
    async def get_voice_profile(self, member_id: int) -> Optional[Dict[str, Any]]:
        """
        获取声纹信息
        
        Args:
            member_id: 成员ID
            
        Returns:
            声纹信息
        """
        try:
            from app.database.voice_profile_dao import VoiceProfileDAO
            
            async with async_session_maker() as session:
                voice_dao = VoiceProfileDAO(session)
                profile = await voice_dao.get_by_member(member_id)
                
                if not profile:
                    return None
                
                return {
                    "id": profile.id,
                    "member_id": profile.member_id,
                    "model_version": profile.model_version,
                    "sample_count": profile.sample_count,
                    "is_active": profile.is_active,
                    "created_at": profile.created_at.isoformat(),
                    "updated_at": profile.updated_at.isoformat()
                }
                
        except Exception as e:
            print(f"获取声纹信息失败: {e}")
            return None
    
    async def delete_voice_profile(self, member_id: int) -> bool:
        """
        删除声纹
        
        Args:
            member_id: 成员ID
            
        Returns:
            删除是否成功
        """
        try:
            return await self.engine.delete_speaker(member_id)
            
        except Exception as e:
            print(f"删除声纹失败: {e}")
            return False
    
    async def identify_speaker(
        self, 
        audio_path: str, 
        threshold: Optional[float] = None
    ) -> Tuple[Optional[FamilyMember], float]:
        """
        识别说话人
        
        Args:
            audio_path: 音频文件路径
            threshold: 相似度阈值
            
        Returns:
            tuple: (识别到的成员, 相似度分数)
        """
        try:
            # 识别说话人
            member_id, similarity, details = await self.engine.identify_speaker(
                audio_path=audio_path,
                threshold=threshold or self.similarity_threshold
            )
            
            if member_id:
                # 获取成员信息
                async with async_session_maker() as session:
                    member_dao = FamilyMemberDAO(session)
                    member = await member_dao.get_by_id(member_id)
                    return member, similarity
            
            return None, similarity
            
        except Exception as e:
            print(f"识别说话人失败: {e}")
            return None, 0.0
    
    async def get_all_voice_profiles(self) -> List[Dict[str, Any]]:
        """
        获取所有声纹信息
        
        Returns:
            所有声纹信息列表
        """
        try:
            from app.database.voice_profile_dao import VoiceProfileDAO
            
            async with async_session_maker() as session:
                voice_dao = VoiceProfileDAO(session)
                profiles = await voice_dao.get_all_active()
                
                result = []
                for profile in profiles:
                    result.append({
                        "id": profile.id,
                        "member_id": profile.member_id,
                        "model_version": profile.model_version,
                        "sample_count": profile.sample_count,
                        "is_active": profile.is_active,
                        "created_at": profile.created_at.isoformat(),
                        "updated_at": profile.updated_at.isoformat()
                    })
                
                return result
                
        except Exception as e:
            print(f"获取声纹信息列表失败: {e}")
            return []
    
    async def validate_audio_for_voiceprint(self, audio_path: str) -> Dict[str, Any]:
        """
        验证音频文件是否适合声纹识别
        
        Args:
            audio_path: 音频文件路径
            
        Returns:
            验证结果
        """
        try:
            import librosa
            
            # 读取音频文件
            y, sr = librosa.load(audio_path, sr=None)
            duration = librosa.get_duration(y=y, sr=sr)
            
            issues = []
            
            # 检查音频时长
            min_duration = config_manager.get('voice_recognition.min_audio_length', 2.0)
            max_duration = config_manager.get('voice_recognition.max_audio_length', 30.0)
            
            if duration < min_duration:
                issues.append(f"音频时长过短: {duration:.1f}s < {min_duration}s")
            
            if duration > max_duration:
                issues.append(f"音频时长过长: {duration:.1f}s > {max_duration}s")
            
            # 检查音频质量
            if np.max(np.abs(y)) < 0.01:
                issues.append("音频声音过小")
            
            # 检查采样率
            recommended_sr = 16000
            if sr < 8000:
                issues.append(f"采样率过低: {sr}Hz < 8000Hz")
            
            return {
                "valid": len(issues) == 0,
                "issues": issues,
                "info": {
                    "duration": duration,
                    "sample_rate": sr,
                    "channels": 1 if len(y.shape) == 1 else y.shape[1],
                    "recommended_sr": recommended_sr,
                    "quality_score": min(1.0, np.max(np.abs(y)) * 10)  # 简单的质量评分
                }
            }
            
        except Exception as e:
            return {
                "valid": False,
                "issues": [f"音频文件检查失败: {str(e)}"],
                "info": {}
            }
    
    async def get_engine_info(self) -> Dict[str, Any]:
        """获取引擎信息"""
        return self.engine.get_model_info()
    
    def cleanup(self):
        """清理资源"""
        if self.engine:
            self.engine.cleanup()