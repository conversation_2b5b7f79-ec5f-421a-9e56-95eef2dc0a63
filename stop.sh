#!/bin/bash

echo "====================================="
echo "  J_family00 智能家庭助手停止脚本"
echo "====================================="
echo

# 设置工作目录
cd "$(dirname "$0")"

# 停止后端服务
if [ -f "backend.pid" ]; then
    BACKEND_PID=$(cat backend.pid)
    echo "[信息] 停止后端服务 (PID: $BACKEND_PID)..."
    kill $BACKEND_PID 2>/dev/null
    if [ $? -eq 0 ]; then
        echo "[成功] 后端服务已停止"
    else
        echo "[警告] 后端服务可能已经停止"
    fi
    rm backend.pid
else
    echo "[警告] 未找到后端服务PID文件"
fi

# 停止前端服务
if [ -f "frontend.pid" ]; then
    FRONTEND_PID=$(cat frontend.pid)
    echo "[信息] 停止前端服务 (PID: $FRONTEND_PID)..."
    kill $FRONTEND_PID 2>/dev/null
    if [ $? -eq 0 ]; then
        echo "[成功] 前端服务已停止"
    else
        echo "[警告] 前端服务可能已经停止"
    fi
    rm frontend.pid
else
    echo "[警告] 未找到前端服务PID文件"
fi

# 强制杀死可能残留的进程
echo "[信息] 清理残留进程..."
pkill -f "python main.py" 2>/dev/null
pkill -f "npm start" 2>/dev/null
pkill -f "react-scripts start" 2>/dev/null

echo
echo "====================================="
echo "  所有服务已停止"
echo "====================================="