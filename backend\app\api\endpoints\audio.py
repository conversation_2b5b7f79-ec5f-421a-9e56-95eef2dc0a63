"""
音频处理端点
"""

from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File
from fastapi.responses import FileResponse
from typing import Optional
import os
from pathlib import Path
from app.core.config import settings
from app.core.logger import get_logger

logger = get_logger("api")

router = APIRouter()


@router.get("/files/{file_path:path}")
async def serve_audio_file(file_path: str):
    """提供音频文件访问服务"""
    try:
        # 构建完整的文件路径
        audio_dir = Path(settings.AUDIO_DIR)
        full_path = audio_dir / file_path

        # 安全检查：确保文件在音频目录内
        try:
            full_path.resolve().relative_to(audio_dir.resolve())
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="访问被拒绝"
            )

        # 检查文件是否存在
        if not full_path.exists() or not full_path.is_file():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="音频文件不存在"
            )

        # 确定媒体类型
        media_type = "audio/wav"
        if full_path.suffix.lower() == ".mp3":
            media_type = "audio/mpeg"
        elif full_path.suffix.lower() == ".webm":
            media_type = "audio/webm"

        return FileResponse(
            path=str(full_path),
            media_type=media_type,
            filename=full_path.name
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"音频文件服务失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"音频文件服务失败: {str(e)}"
        )


@router.get("/info")
async def get_audio_info():
    """获取音频服务信息"""
    try:
        audio_dir = Path(settings.AUDIO_DIR)
        tts_dir = audio_dir / "tts"

        # 统计文件数量
        total_files = len(list(audio_dir.rglob("*.*"))) if audio_dir.exists() else 0
        tts_files = len(list(tts_dir.glob("*.wav"))) if tts_dir.exists() else 0

        return {
            "audio_dir": str(audio_dir),
            "total_files": total_files,
            "tts_files": tts_files,
            "supported_formats": ["wav", "mp3", "webm"],
            "status": "ready"
        }
    except Exception as e:
        logger.error(f"获取音频信息失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取音频信息失败: {str(e)}"
        )


@router.delete("/cleanup")
async def cleanup_old_files(max_age_hours: int = 24):
    """清理旧的音频文件"""
    try:
        import time
        from pathlib import Path

        audio_dir = Path(settings.AUDIO_DIR)
        current_time = time.time()
        max_age_seconds = max_age_hours * 3600
        deleted_count = 0

        # 清理TTS文件
        tts_dir = audio_dir / "tts"
        if tts_dir.exists():
            for file_path in tts_dir.glob("tts_*.wav"):
                if file_path.is_file():
                    file_age = current_time - file_path.stat().st_mtime
                    if file_age > max_age_seconds:
                        file_path.unlink()
                        deleted_count += 1

        logger.info(f"清理了 {deleted_count} 个旧的音频文件")
        return {
            "message": f"清理完成，删除了 {deleted_count} 个文件",
            "deleted_count": deleted_count
        }

    except Exception as e:
        logger.error(f"清理音频文件失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"清理音频文件失败: {str(e)}"
        )


