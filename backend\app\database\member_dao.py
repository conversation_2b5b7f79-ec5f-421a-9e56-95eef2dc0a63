"""
家庭成员数据访问对象 (DAO)
"""

from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete
from sqlalchemy.orm import selectinload
from app.models.database import FamilyMember
from app.models.schemas import FamilyMemberCreate, FamilyMemberUpdate


class FamilyMemberDAO:
    """家庭成员数据访问对象"""
    
    def __init__(self, session: AsyncSession):
        self.session = session
    
    async def create(self, member_data: FamilyMemberCreate) -> FamilyMember:
        """创建家庭成员"""
        member = FamilyMember(
            name=member_data.name,
            nickname=member_data.nickname,
        )
        
        # 设置记忆文件路径
        member.memory_file_path = f"data/memories/{member_data.name}_{member.id}.json"
        
        self.session.add(member)
        await self.session.commit()
        await self.session.refresh(member)
        return member
    
    async def get_by_id(self, member_id: int) -> Optional[FamilyMember]:
        """根据ID获取家庭成员"""
        result = await self.session.execute(
            select(FamilyMember)
            .where(FamilyMember.id == member_id)
            .options(selectinload(FamilyMember.conversations))
        )
        return result.scalar_one_or_none()
    
    async def get_by_name(self, name: str) -> Optional[FamilyMember]:
        """根据姓名获取家庭成员"""
        result = await self.session.execute(
            select(FamilyMember)
            .where(FamilyMember.name == name)
        )
        return result.scalar_one_or_none()
    
    async def get_all(self, skip: int = 0, limit: int = 100) -> List[FamilyMember]:
        """获取所有家庭成员"""
        result = await self.session.execute(
            select(FamilyMember)
            .where(FamilyMember.is_active == True)
            .offset(skip)
            .limit(limit)
            .order_by(FamilyMember.created_at.desc())
        )
        return result.scalars().all()
    
    async def update(self, member_id: int, member_update: FamilyMemberUpdate) -> Optional[FamilyMember]:
        """更新家庭成员"""
        # 构建更新数据
        update_data = {}
        if member_update.name is not None:
            update_data["name"] = member_update.name
        if member_update.nickname is not None:
            update_data["nickname"] = member_update.nickname
        if member_update.is_active is not None:
            update_data["is_active"] = member_update.is_active
        
        if not update_data:
            return await self.get_by_id(member_id)
        
        # 执行更新
        await self.session.execute(
            update(FamilyMember)
            .where(FamilyMember.id == member_id)
            .values(**update_data)
        )
        await self.session.commit()
        
        return await self.get_by_id(member_id)
    
    async def delete(self, member_id: int) -> bool:
        """删除家庭成员（软删除）"""
        result = await self.session.execute(
            update(FamilyMember)
            .where(FamilyMember.id == member_id)
            .values(is_active=False)
        )
        await self.session.commit()
        return result.rowcount > 0
    
    async def hard_delete(self, member_id: int) -> bool:
        """硬删除家庭成员"""
        result = await self.session.execute(
            delete(FamilyMember)
            .where(FamilyMember.id == member_id)
        )
        await self.session.commit()
        return result.rowcount > 0
    
    async def update_voice_profile_path(self, member_id: int, voice_profile_path: str) -> bool:
        """更新声纹文件路径"""
        result = await self.session.execute(
            update(FamilyMember)
            .where(FamilyMember.id == member_id)
            .values(voice_profile_path=voice_profile_path)
        )
        await self.session.commit()
        return result.rowcount > 0
    
    async def get_active_members(self) -> List[FamilyMember]:
        """获取所有活跃成员"""
        result = await self.session.execute(
            select(FamilyMember)
            .where(FamilyMember.is_active == True)
            .order_by(FamilyMember.name)
        )
        return result.scalars().all()