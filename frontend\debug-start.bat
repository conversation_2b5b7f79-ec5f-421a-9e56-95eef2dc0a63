@echo off
echo 开始调试前端启动问题...

echo.
echo 检查Node.js版本:
node --version

echo.
echo 检查npm版本:
npm --version

echo.
echo 检查当前目录:
cd

echo.
echo 检查package.json是否存在:
if exist package.json (
    echo package.json 存在
) else (
    echo package.json 不存在
)

echo.
echo 检查src目录:
if exist src (
    echo src目录存在
    dir src
) else (
    echo src目录不存在
)

echo.
echo 检查node_modules:
if exist node_modules (
    echo node_modules存在
) else (
    echo node_modules不存在
)

echo.
echo 尝试启动开发服务器...
npm start

pause
