#!/bin/bash

# WeSpeaker 声纹识别模型下载脚本

echo "=================================="
echo "  WeSpeaker 声纹识别模型下载工具"
echo "=================================="
echo

# 创建模型目录
MODELS_DIR="./models/wespeaker"
mkdir -p "$MODELS_DIR"

# 推荐的声纹识别模型列表
echo "推荐的WeSpeaker声纹识别模型："
echo "1. VoxCeleb ResNet34 (轻量级，适合CPU推理)"
echo "2. VoxCeleb ECAPA-TDNN (高精度，适合GPU推理)"
echo "3. CN-Celeb ResNet34 (中文优化)"

echo
read -p "请选择要下载的模型 (1-3): " choice

case $choice in
    1)
        MODEL_NAME="voxceleb_resnet34"
        MODEL_URL="https://github.com/mobvoi/wespeaker/releases/download/models/voxceleb_resnet34.onnx"
        ;;
    2)
        MODEL_NAME="voxceleb_ecapa_tdnn"
        MODEL_URL="https://github.com/mobvoi/wespeaker/releases/download/models/voxceleb_ecapa_tdnn.onnx"
        ;;
    3)
        MODEL_NAME="cnceleb_resnet34"
        MODEL_URL="https://github.com/mobvoi/wespeaker/releases/download/models/cnceleb_resnet34.onnx"
        ;;
    *)
        echo "无效选择，退出"
        exit 1
        ;;
esac

echo
echo "开始下载模型: $MODEL_NAME"
echo "下载地址: $MODEL_URL"

# 检查是否安装了wget或curl
if command -v wget &> /dev/null; then
    DOWNLOAD_CMD="wget -O"
elif command -v curl &> /dev/null; then
    DOWNLOAD_CMD="curl -L -o"
else
    echo "错误: 未找到 wget 或 curl，请手动下载模型"
    echo "下载地址: $MODEL_URL"
    echo "保存到: $MODELS_DIR/${MODEL_NAME}.onnx"
    exit 1
fi

# 下载模型
cd "$MODELS_DIR"
echo "正在下载..."
$DOWNLOAD_CMD "${MODEL_NAME}.onnx" "$MODEL_URL"

if [ $? -eq 0 ]; then
    echo
    echo "=================================="
    echo "  模型下载完成！"
    echo "=================================="
    echo "模型路径: $MODELS_DIR/${MODEL_NAME}.onnx"
    echo
    echo "模型信息:"
    ls -la "${MODEL_NAME}.onnx"
    echo
    echo "现在可以启动 J_family00 声纹识别服务了！"
    echo
    echo "使用说明："
    echo "1. 确保已安装 onnxruntime: pip install onnxruntime"
    echo "2. 为家庭成员注册声纹"
    echo "3. 享受智能声纹识别功能"
    
else
    echo "下载失败，请检查网络连接或手动下载"
    echo "手动下载地址: $MODEL_URL"
    echo "手动下载说明："
    echo "1. 访问 WeSpeaker GitHub 仓库"
    echo "2. 下载预训练模型"
    echo "3. 将 .onnx 文件放置到 $MODELS_DIR/ 目录"
    exit 1
fi