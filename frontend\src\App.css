/* App.css */
.App {
  text-align: center;
}

.ant-layout-header {
  display: flex;
  align-items: center;
  justify-content: center;
}

.ant-list-item {
  transition: background-color 0.3s;
}

.ant-list-item:hover {
  background-color: #f5f5f5;
}

.ant-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.conversation-input {
  display: flex;
  gap: 8px;
  align-items: flex-end;
}

.audio-upload {
  display: flex;
  align-items: center;
}

/* 语音录制按钮样式 */
.recording {
  animation: pulse 1s infinite;
  background-color: #ff4d4f !important;
  border-color: #ff4d4f !important;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 77, 79, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(255, 77, 79, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 77, 79, 0);
  }
}

/* 语音播放动画 */
@keyframes speaking-pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 语音波形动画 */
@keyframes wave {
  0%, 100% {
    transform: scaleY(1);
  }
  50% {
    transform: scaleY(1.5);
  }
}

.voice-indicator {
  animation: speaking-pulse 1.5s infinite;
}

.speaking-animation {
  position: relative;
}

.speaking-animation::after {
  content: '';
  position: absolute;
  top: 50%;
  right: -20px;
  width: 3px;
  height: 10px;
  background: #52c41a;
  animation: wave 0.8s infinite;
  transform-origin: bottom;
}

/* AI对话气泡动画 */
.ai-message-speaking {
  animation: gentle-glow 2s infinite;
}

@keyframes gentle-glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(82, 196, 26, 0.3);
  }
  50% {
    box-shadow: 0 0 15px rgba(82, 196, 26, 0.6);
  }
}