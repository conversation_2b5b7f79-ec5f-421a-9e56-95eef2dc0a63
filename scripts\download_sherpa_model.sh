#!/bin/bash

# Sherpa-ncnn 中文语音识别模型下载脚本

echo "=================================="
echo "  Sherpa-ncnn 中文模型下载工具"
echo "=================================="
echo

# 创建模型目录
MODELS_DIR="./models"
mkdir -p "$MODELS_DIR"

# 推荐的中文模型列表
echo "推荐的中文语音识别模型："
echo "1. sherpa-ncnn-streaming-zipformer-zh-14M-2023-02-23 (轻量级，14M)"
echo "2. sherpa-ncnn-streaming-zipformer-bilingual-zh-en-2023-02-13 (中英双语)"
echo "3. sherpa-ncnn-streaming-zipformer-zh-20M-2023-02-17 (中等大小，20M)"

echo
read -p "请选择要下载的模型 (1-3): " choice

case $choice in
    1)
        MODEL_NAME="sherpa-ncnn-streaming-zipformer-zh-14M-2023-02-23"
        MODEL_URL="https://github.com/k2-fsa/sherpa-ncnn/releases/download/models/${MODEL_NAME}.tar.bz2"
        ;;
    2)
        MODEL_NAME="sherpa-ncnn-streaming-zipformer-bilingual-zh-en-2023-02-13"
        MODEL_URL="https://github.com/k2-fsa/sherpa-ncnn/releases/download/models/${MODEL_NAME}.tar.bz2"
        ;;
    3)
        MODEL_NAME="sherpa-ncnn-streaming-zipformer-zh-20M-2023-02-17"
        MODEL_URL="https://github.com/k2-fsa/sherpa-ncnn/releases/download/models/${MODEL_NAME}.tar.bz2"
        ;;
    *)
        echo "无效选择，退出"
        exit 1
        ;;
esac

echo
echo "开始下载模型: $MODEL_NAME"
echo "下载地址: $MODEL_URL"

# 检查是否安装了wget或curl
if command -v wget &> /dev/null; then
    DOWNLOAD_CMD="wget -O"
elif command -v curl &> /dev/null; then
    DOWNLOAD_CMD="curl -L -o"
else
    echo "错误: 未找到 wget 或 curl，请手动下载模型"
    echo "下载地址: $MODEL_URL"
    echo "解压到: $MODELS_DIR/sherpa-ncnn-zh/"
    exit 1
fi

# 下载模型
cd "$MODELS_DIR"
echo "正在下载..."
$DOWNLOAD_CMD "${MODEL_NAME}.tar.bz2" "$MODEL_URL"

if [ $? -eq 0 ]; then
    echo "下载完成，正在解压..."
    
    # 解压文件
    tar -xjf "${MODEL_NAME}.tar.bz2"
    
    if [ $? -eq 0 ]; then
        # 重命名为标准目录名
        if [ -d "$MODEL_NAME" ]; then
            mv "$MODEL_NAME" "sherpa-ncnn-zh"
        fi
        
        # 删除压缩包
        rm "${MODEL_NAME}.tar.bz2"
        
        echo
        echo "=================================="
        echo "  模型下载和安装完成！"
        echo "=================================="
        echo "模型路径: $MODELS_DIR/sherpa-ncnn-zh/"
        echo
        echo "模型文件列表:"
        ls -la "sherpa-ncnn-zh/"
        echo
        echo "现在可以启动 J_family00 语音识别服务了！"
        
    else
        echo "解压失败，请手动解压: ${MODEL_NAME}.tar.bz2"
        exit 1
    fi
else
    echo "下载失败，请检查网络连接或手动下载"
    echo "手动下载地址: $MODEL_URL"
    exit 1
fi