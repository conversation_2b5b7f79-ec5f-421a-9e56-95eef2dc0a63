"""
Sherpa-ncnn语音识别引擎
基于K2-FSA团队的下一代Kaldi项目，由<PERSON>团队开发
"""

import os
import asyncio
import logging
from typing import Optional, Tuple, Dict, Any, List
from pathlib import Path
import sherpa_ncnn
import soundfile as sf
import numpy as np
from app.core.config import settings
from app.core.config_manager import config_manager


class SherpaNcnnEngine:
    """Sherpa-ncnn语音识别引擎"""
    
    def __init__(self):
        self.recognizer = None
        self.sample_rate = 16000  # Sherpa-ncnn推荐采样率
        self._is_initialized = False
        self._model_config = None
        
        # 配置日志
        logging.getLogger('sherpa_ncnn').setLevel(logging.WARNING)
    
    async def initialize(self, model_path: Optional[str] = None) -> bool:
        """
        初始化Sherpa-ncnn模型
        
        Args:
            model_path: 模型路径，如果为None则使用默认配置
        """
        if self._is_initialized:
            return True
        
        try:
            # 获取模型配置
            model_config = await self._get_model_config(model_path)
            if not model_config:
                return False
            
            print(f"正在加载Sherpa-ncnn模型: {model_config['model_name']}")
            
            # 在线程池中初始化识别器
            loop = asyncio.get_event_loop()
            self.recognizer = await loop.run_in_executor(
                None,
                self._create_recognizer,
                model_config
            )
            
            if self.recognizer is None:
                print("Sherpa-ncnn识别器创建失败")
                return False
            
            self._model_config = model_config
            self._is_initialized = True
            print(f"Sherpa-ncnn模型加载成功: {model_config['model_name']}")
            return True
            
        except Exception as e:
            print(f"Sherpa-ncnn模型初始化失败: {e}")
            return False
    
    def _create_recognizer(self, model_config: Dict[str, Any]) -> Optional[sherpa_ncnn.Recognizer]:
        """创建Sherpa-ncnn识别器"""
        try:
            # 创建配置对象
            config = sherpa_ncnn.RecognizerConfig(
                feat_config=sherpa_ncnn.FeatureConfig(
                    sample_rate=self.sample_rate,
                    feature_dim=80
                ),
                model_config=sherpa_ncnn.ModelConfig(
                    encoder_param=model_config['encoder_param'],
                    encoder_bin=model_config['encoder_bin'],
                    decoder_param=model_config['decoder_param'],
                    decoder_bin=model_config['decoder_bin'],
                    joiner_param=model_config['joiner_param'],
                    joiner_bin=model_config['joiner_bin'],
                    tokens=model_config['tokens'],
                    num_threads=2,
                    use_vulkan_compute=False  # 使用CPU推理
                ),
                decoder_config=sherpa_ncnn.DecoderConfig(
                    decoding_method="greedy_search",
                    num_active_paths=4
                ),
                enable_endpoint=True,
                rule1_min_trailing_silence=2.4,
                rule2_min_trailing_silence=1.2,
                rule3_min_utterance_length=300
            )
            
            # 创建识别器
            recognizer = sherpa_ncnn.Recognizer(config)
            return recognizer
            
        except Exception as e:
            print(f"创建识别器失败: {e}")
            return None
    
    async def _get_model_config(self, model_path: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """获取模型配置"""
        if model_path:
            model_dir = Path(model_path)
        else:
            # 使用默认模型路径
            model_dir = Path(settings.MODELS_DIR) / "sherpa-ncnn-zh"
        
        if not model_dir.exists():
            print(f"模型目录不存在: {model_dir}")
            await self._download_default_model(model_dir)
            
            # 再次检查
            if not model_dir.exists():
                print("请手动下载Sherpa-ncnn中文模型")
                print("下载地址: https://github.com/k2-fsa/sherpa-ncnn/releases")
                return None
        
        # 构建模型文件路径
        config = {
            'model_name': model_dir.name,
            'encoder_param': str(model_dir / "encoder_jit_trace-pnnx.ncnn.param"),
            'encoder_bin': str(model_dir / "encoder_jit_trace-pnnx.ncnn.bin"),
            'decoder_param': str(model_dir / "decoder_jit_trace-pnnx.ncnn.param"),
            'decoder_bin': str(model_dir / "decoder_jit_trace-pnnx.ncnn.bin"),
            'joiner_param': str(model_dir / "joiner_jit_trace-pnnx.ncnn.param"),
            'joiner_bin': str(model_dir / "joiner_jit_trace-pnnx.ncnn.bin"),
            'tokens': str(model_dir / "tokens.txt")
        }
        
        # 验证所有文件是否存在
        for key, file_path in config.items():
            if key == 'model_name':
                continue
            if not Path(file_path).exists():
                print(f"模型文件缺失: {file_path}")
                return None
        
        return config
    
    async def _download_default_model(self, model_dir: Path):
        """下载默认的中文模型（示例实现）"""
        print("正在尝试下载默认中文模型...")
        print("请手动下载模型文件到以下路径：")
        print(f"  {model_dir}")
        print("推荐模型：")
        print("  - sherpa-ncnn-streaming-zipformer-zh-14M-2023-02-23")
        print("  - sherpa-ncnn-streaming-zipformer-bilingual-zh-en-2023-02-13")
        print("下载地址：https://github.com/k2-fsa/sherpa-ncnn/releases")
    
    async def transcribe(
        self, 
        audio_path: str, 
        language: Optional[str] = "zh"
    ) -> Tuple[str, float, Dict[str, Any]]:
        """
        转录音频文件
        
        Args:
            audio_path: 音频文件路径
            language: 语言代码（sherpa-ncnn根据模型自动确定）
            
        Returns:
            tuple: (转录文本, 置信度, 详细信息)
        """
        if not self._is_initialized:
            if not await self.initialize():
                raise RuntimeError("Sherpa-ncnn模型初始化失败")
        
        if not os.path.exists(audio_path):
            raise FileNotFoundError(f"音频文件不存在: {audio_path}")
        
        try:
            # 在线程池中执行转录
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                None,
                self._transcribe_audio_file,
                audio_path
            )
            
            return result
            
        except Exception as e:
            print(f"Sherpa-ncnn转录失败: {e}")
            raise RuntimeError(f"语音转录失败: {str(e)}")
    
    def _transcribe_audio_file(self, audio_path: str) -> Tuple[str, float, Dict[str, Any]]:
        """转录音频文件（同步方法）"""
        try:
            # 读取音频文件
            samples, orig_sample_rate = sf.read(audio_path, dtype='float32')
            
            # 确保是单声道
            if len(samples.shape) > 1:
                samples = np.mean(samples, axis=1)
            
            # 重采样到16kHz
            if orig_sample_rate != self.sample_rate:
                import librosa
                samples = librosa.resample(samples, orig_sr=orig_sample_rate, target_sr=self.sample_rate)
            
            # 创建音频流
            stream = self.recognizer.create_stream()
            
            # 分块处理音频
            chunk_size = int(0.1 * self.sample_rate)  # 100ms chunks
            text_segments = []
            
            for i in range(0, len(samples), chunk_size):
                chunk = samples[i:i + chunk_size]
                stream.accept_waveform(self.sample_rate, chunk)
                
                # 检查是否有结果
                if self.recognizer.is_ready(stream):
                    self.recognizer.decode_stream(stream)
                
                result = self.recognizer.get_result(stream)
                if result.text:
                    text_segments.append(result.text)
                    self.recognizer.reset(stream)
            
            # 处理最后的数据
            stream.input_finished()
            if self.recognizer.is_ready(stream):
                self.recognizer.decode_stream(stream)
            
            final_result = self.recognizer.get_result(stream)
            if final_result.text:
                text_segments.append(final_result.text)
            
            # 合并文本
            full_text = ''.join(text_segments).strip()
            
            # 计算置信度（sherpa-ncnn不直接提供置信度，使用启发式方法）
            confidence = self._estimate_confidence(full_text, len(samples) / self.sample_rate)
            
            # 获取音频时长
            duration = len(samples) / self.sample_rate
            
            # 详细信息
            details = {
                "language": language or "zh",
                "duration": duration,
                "segments_count": len(text_segments),
                "model": self._model_config['model_name'] if self._model_config else "sherpa-ncnn",
                "engine": "Sherpa-ncnn",
                "sample_rate": self.sample_rate,
                "audio_length": len(samples)
            }
            
            return full_text, confidence, details
            
        except Exception as e:
            raise RuntimeError(f"音频转录处理失败: {str(e)}")
    
    def _estimate_confidence(self, text: str, duration: float) -> float:
        """估算置信度（启发式方法）"""
        if not text:
            return 0.0
        
        # 基于文本长度和音频时长的启发式置信度
        text_length = len(text.strip())
        if text_length == 0:
            return 0.0
        
        # 基础置信度
        base_confidence = 0.8
        
        # 根据文本密度调整
        if duration > 0:
            chars_per_second = text_length / duration
            # 合理的中文语速大约是2-4字符/秒
            if 1.5 <= chars_per_second <= 5.0:
                density_bonus = 0.1
            else:
                density_bonus = -0.1
        else:
            density_bonus = 0.0
        
        # 根据文本长度调整
        if text_length >= 10:
            length_bonus = 0.1
        elif text_length >= 5:
            length_bonus = 0.0
        else:
            length_bonus = -0.2
        
        confidence = base_confidence + density_bonus + length_bonus
        return max(0.0, min(1.0, confidence))
    
    async def transcribe_realtime(
        self, 
        audio_chunk: np.ndarray,
        is_final: bool = False
    ) -> Tuple[str, bool]:
        """
        实时转录音频数据
        
        Args:
            audio_chunk: 音频数据块（numpy数组）
            is_final: 是否为最终数据
            
        Returns:
            tuple: (转录文本, 是否为完整句子)
        """
        if not self._is_initialized:
            if not await self.initialize():
                raise RuntimeError("Sherpa-ncnn模型初始化失败")
        
        try:
            # 这里需要维护一个流状态，简化实现
            # 实际应用中应该为每个会话维护独立的流
            if not hasattr(self, '_realtime_stream'):
                self._realtime_stream = self.recognizer.create_stream()
            
            stream = self._realtime_stream
            
            # 接受音频数据
            stream.accept_waveform(self.sample_rate, audio_chunk.astype(np.float32))
            
            if is_final:
                stream.input_finished()
            
            # 解码
            if self.recognizer.is_ready(stream):
                self.recognizer.decode_stream(stream)
            
            # 获取结果
            result = self.recognizer.get_result(stream)
            text = result.text if result else ""
            
            # 判断是否为完整句子
            is_complete = bool(text and (is_final or text.endswith(('。', '！', '？', '.', '!', '?'))))
            
            if is_complete:
                # 重置流以开始新的识别
                self.recognizer.reset(stream)
            
            return text, is_complete
            
        except Exception as e:
            print(f"实时转录失败: {e}")
            return "", False
    
    def get_supported_languages(self) -> Dict[str, str]:
        """获取支持的语言列表"""
        # Sherpa-ncnn支持的语言取决于具体的模型
        return {
            "zh": "中文",
            "en": "English",
            "zh-en": "中英混合"
        }
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return {
            "name": "Sherpa-ncnn",
            "version": sherpa_ncnn.__version__ if hasattr(sherpa_ncnn, '__version__') else "unknown",
            "model_config": self._model_config,
            "initialized": self._is_initialized,
            "supported_languages": list(self.get_supported_languages().keys()),
            "capabilities": [
                "流式识别",
                "离线工作",
                "CPU推理",
                "跨平台支持",
                "高准确率",
                "低延迟"
            ],
            "features": [
                "基于K2-FSA架构",
                "Daniel Povey团队开发",
                "NCNN推理引擎",
                "轻量级部署",
                "中文优化"
            ],
            "requirements": [
                "16kHz采样率（推荐）",
                "单声道",
                "float32格式"
            ]
        }
    
    def cleanup(self):
        """清理资源"""
        if hasattr(self, '_realtime_stream'):
            del self._realtime_stream
        
        if self.recognizer is not None:
            del self.recognizer
            self.recognizer = None
        
        self._is_initialized = False
        self._model_config = None
        print("Sherpa-ncnn引擎资源已清理")
    
    def reset_realtime_stream(self):
        """重置实时识别流"""
        if hasattr(self, '_realtime_stream'):
            del self._realtime_stream