"""
阿里百炼Qwen大语言模型服务
"""

import json
import asyncio
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
import dashscope
from dashscope import Generation
from app.core.config import settings
from app.core.logger import get_logger
from app.core.exceptions import ModelInitializationError, ValidationError
from app.services.memory.memory_service import MemoryService
from app.models.schemas import MemoryType, MemoryCreate

logger = get_logger("llm")


class QwenLLMService:
    """Qwen大语言模型服务"""
    
    def __init__(self):
        self.api_key = settings.DASHSCOPE_API_KEY
        self.model_name = settings.QWEN_MODEL_NAME
        self.max_tokens = settings.QWEN_MAX_TOKENS
        self.temperature = settings.QWEN_TEMPERATURE
        self.system_prompt = settings.AI_SYSTEM_PROMPT
        self.memory_service = MemoryService()
        self._initialize_client()
    
    def _initialize_client(self):
        """初始化阿里百炼客户端"""
        if not self.api_key:
            raise ModelInitializationError(
                "阿里百炼API密钥未配置，请在.env文件中设置DASHSCOPE_API_KEY",
                model_name="qwen"
            )
        
        # 设置API密钥
        dashscope.api_key = self.api_key
        logger.info(f"Qwen LLM服务初始化完成，模型: {self.model_name}")
    
    async def generate_response(
        self,
        user_input: str,
        member_id: Optional[int] = None,
        member_name: Optional[str] = None,
        conversation_history: Optional[List[Dict[str, str]]] = None
    ) -> Dict[str, Any]:
        """
        生成AI回复
        
        Args:
            user_input: 用户输入
            member_id: 家庭成员ID
            member_name: 家庭成员姓名
            conversation_history: 对话历史
            
        Returns:
            包含回复内容和相关信息的字典
        """
        try:
            # 构建对话消息
            messages = await self._build_messages(
                user_input, member_id, member_name, conversation_history
            )
            
            # 调用Qwen模型
            response = await self._call_qwen_api(messages)
            logger.info(f"Qwen API原始响应: {response}")
            
            # 处理回复
            ai_response = ""
            if "choices" in response and len(response["choices"]) > 0:
                # 标准的OpenAI格式响应
                ai_response = response["choices"][0].get("message", {}).get("content", "")
            elif "text" in response and response["text"]:
                # 直接文本响应
                ai_response = response["text"]
            elif "output" in response and "text" in response["output"]:
                # 嵌套的输出格式
                ai_response = response["output"]["text"]
            else:
                # 如果都没有，记录原始响应用于调试
                logger.warning(f"无法解析的响应格式: {response}")
                ai_response = "抱歉，我现在有点累了，请稍后再试试吧~ 💕"

            usage = response.get("usage", {})
            
            # 保存对话到记忆
            if member_id and ai_response:
                await self._save_conversation_memory(
                    member_id, member_name or f"member_{member_id}", 
                    user_input, ai_response
                )
            
            logger.info(f"Qwen回复生成成功，用户: {member_name}, tokens: {usage}")
            
            return {
                "response": ai_response,
                "model": self.model_name,
                "usage": usage,
                "success": True,
                "timestamp": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Qwen回复生成失败: {str(e)}")
            return {
                "response": "抱歉，我现在有点累了，请稍后再试试吧~ 💕",
                "model": self.model_name,
                "error": str(e),
                "success": False,
                "timestamp": datetime.utcnow().isoformat()
            }
    
    async def _build_messages(
        self,
        user_input: str,
        member_id: Optional[int],
        member_name: Optional[str],
        conversation_history: Optional[List[Dict[str, str]]]
    ) -> List[Dict[str, str]]:
        """构建对话消息列表"""
        messages = []
        
        # 系统提示词
        system_message = self.system_prompt
        
        # 添加个人记忆信息
        if member_id:
            personal_context = await self._get_personal_context(member_id, member_name)
            if personal_context:
                system_message += f"\n\n关于{member_name or '这位家人'}的信息：\n{personal_context}"
        
        messages.append({
            "role": "system",
            "content": system_message
        })
        
        # 添加对话历史（最近5轮）
        if conversation_history:
            recent_history = conversation_history[-5:]  # 只保留最近5轮对话
            for conv in recent_history:
                if conv.get("input_text"):
                    messages.append({
                        "role": "user",
                        "content": conv["input_text"]
                    })
                if conv.get("response_text"):
                    messages.append({
                        "role": "assistant",
                        "content": conv["response_text"]
                    })
        
        # 添加当前用户输入
        messages.append({
            "role": "user",
            "content": user_input
        })
        
        return messages
    
    async def _get_personal_context(self, member_id: int, member_name: str) -> str:
        """获取个人上下文信息"""
        try:
            # 获取成员记忆
            memories = await self.memory_service.get_member_memories(member_id, member_name)
            if not memories:
                return ""
            
            context_parts = []
            
            # 偏好信息
            preferences = memories.get("preferences", {})
            if preferences:
                context_parts.append(f"偏好：{json.dumps(preferences, ensure_ascii=False)}")
            
            # 最近的对话摘要
            recent_conversations = memories.get("conversations", [])[-3:]  # 最近3次对话
            if recent_conversations:
                conv_summaries = [conv.get("summary", "") for conv in recent_conversations if conv.get("summary")]
                if conv_summaries:
                    context_parts.append(f"最近对话：{'; '.join(conv_summaries)}")
            
            # 重要事件
            important_events = memories.get("important_events", [])[-2:]  # 最近2个重要事件
            if important_events:
                event_summaries = [event.get("event", "") for event in important_events if event.get("event")]
                if event_summaries:
                    context_parts.append(f"重要事件：{'; '.join(event_summaries)}")
            
            return "\n".join(context_parts)
            
        except Exception as e:
            logger.warning(f"获取个人上下文失败: {str(e)}")
            return ""
    
    async def _call_qwen_api(self, messages: List[Dict[str, str]]) -> Dict[str, Any]:
        """调用Qwen API"""
        try:
            # 在线程池中执行同步API调用
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None,
                self._sync_call_qwen,
                messages
            )
            
            if response.status_code == 200:
                return response.output
            else:
                raise Exception(f"API调用失败: {response.code} - {response.message}")
                
        except Exception as e:
            logger.error(f"Qwen API调用失败: {str(e)}")
            raise
    
    def _sync_call_qwen(self, messages: List[Dict[str, str]]) -> Any:
        """同步调用Qwen API"""
        return Generation.call(
            model=self.model_name,
            messages=messages,
            max_tokens=self.max_tokens,
            temperature=self.temperature,
            result_format='message'
        )
    
    async def _save_conversation_memory(
        self,
        member_id: int,
        member_name: str,
        user_input: str,
        ai_response: str
    ):
        """保存对话到记忆"""
        try:
            # 生成对话摘要
            summary = await self._generate_conversation_summary(user_input, ai_response)
            
            # 提取关键词
            keywords = await self._extract_keywords(user_input)
            
            # 创建对话记忆
            memory_data = MemoryCreate(
                type=MemoryType.CONVERSATION,
                content={
                    "user_input": user_input,
                    "ai_response": ai_response,
                    "summary": summary,
                    "date": datetime.utcnow().strftime("%Y-%m-%d"),
                    "topic": keywords[0] if keywords else "日常对话"
                },
                keywords=keywords
            )
            
            await self.memory_service.add_memory(member_id, memory_data)
            logger.info(f"对话记忆保存成功: {member_name}")
            
        except Exception as e:
            logger.warning(f"保存对话记忆失败: {str(e)}")
    
    async def _generate_conversation_summary(self, user_input: str, ai_response: str) -> str:
        """生成对话摘要"""
        try:
            # 简单的摘要生成逻辑
            if len(user_input) > 50:
                return user_input[:50] + "..."
            return user_input
        except:
            return "对话摘要生成失败"
    
    async def _extract_keywords(self, text: str) -> List[str]:
        """提取关键词"""
        try:
            # 简单的关键词提取逻辑
            keywords = []
            
            # 常见话题关键词
            topic_keywords = {
                "工作": ["工作", "上班", "公司", "同事", "老板", "项目"],
                "学习": ["学习", "考试", "作业", "学校", "老师", "课程"],
                "家庭": ["家人", "父母", "孩子", "家庭", "亲情"],
                "健康": ["健康", "运动", "锻炼", "身体", "医生"],
                "娱乐": ["电影", "音乐", "游戏", "旅游", "休闲"],
                "情感": ["开心", "难过", "生气", "担心", "兴奋", "感动"]
            }
            
            for topic, words in topic_keywords.items():
                if any(word in text for word in words):
                    keywords.append(topic)
            
            return keywords[:3]  # 最多返回3个关键词
            
        except:
            return ["日常"]
    
    async def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return {
            "model_name": self.model_name,
            "max_tokens": self.max_tokens,
            "temperature": self.temperature,
            "api_configured": bool(self.api_key),
            "status": "ready"
        }
