"""
Pytest配置文件
"""

import asyncio
import pytest
import pytest_asyncio
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from app.core.app import create_app
from app.models.database import Base
from app.core.config import settings


# 测试数据库URL
TEST_DATABASE_URL = "sqlite+aiosqlite:///./test_j_family00.db"


@pytest.fixture(scope="session")
def event_loop():
    """创建事件循环"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest_asyncio.fixture(scope="session")
async def test_engine():
    """创建测试数据库引擎"""
    engine = create_async_engine(TEST_DATABASE_URL, echo=False)
    
    # 创建所有表
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    yield engine
    
    # 清理
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)
    
    await engine.dispose()


@pytest_asyncio.fixture
async def test_session(test_engine):
    """创建测试数据库会话"""
    async_session_maker = sessionmaker(
        test_engine, class_=AsyncSession, expire_on_commit=False
    )
    
    async with async_session_maker() as session:
        yield session


@pytest_asyncio.fixture
async def app():
    """创建测试应用"""
    # 临时修改数据库URL为测试数据库
    original_db_url = settings.DATABASE_URL
    settings.DATABASE_URL = TEST_DATABASE_URL
    
    app = create_app()
    
    yield app
    
    # 恢复原始数据库URL
    settings.DATABASE_URL = original_db_url


@pytest_asyncio.fixture
async def client(app):
    """创建测试客户端"""
    async with AsyncClient(app=app, base_url="http://test") as ac:
        yield ac


@pytest.fixture
def sample_member_data():
    """示例成员数据"""
    return {
        "name": "测试用户",
        "nickname": "小测",
        "is_active": True
    }


@pytest.fixture
def sample_conversation_data():
    """示例对话数据"""
    return {
        "member_id": 1,
        "input_text": "你好，AI家人",
        "response_text": "你好！我是你的专属夸夸官，很高兴见到你！",
        "confidence_score": 0.95
    }


@pytest.fixture
def sample_memory_data():
    """示例记忆数据"""
    return {
        "type": "preference",
        "content": {"favorite_color": "蓝色", "favorite_food": "意大利面"},
        "keywords": ["颜色", "食物", "偏好"]
    }


# 测试工具函数
class TestUtils:
    """测试工具类"""
    
    @staticmethod
    async def create_test_member(session, member_data=None):
        """创建测试成员"""
        from app.database.member_dao import FamilyMemberDAO
        
        if member_data is None:
            member_data = {
                "name": "测试用户",
                "nickname": "小测",
                "is_active": True
            }
        
        member_dao = FamilyMemberDAO(session)
        member = await member_dao.create(member_data)
        await session.commit()
        return member
    
    @staticmethod
    async def create_test_conversation(session, conversation_data=None):
        """创建测试对话"""
        from app.database.conversation_dao import ConversationDAO
        
        if conversation_data is None:
            conversation_data = {
                "member_id": 1,
                "input_text": "测试输入",
                "response_text": "测试回复",
                "confidence_score": 0.9
            }
        
        conversation_dao = ConversationDAO(session)
        conversation = await conversation_dao.create(conversation_data)
        await session.commit()
        return conversation


@pytest.fixture
def test_utils():
    """测试工具实例"""
    return TestUtils
