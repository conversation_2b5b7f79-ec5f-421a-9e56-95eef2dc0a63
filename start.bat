@echo off
echo =====================================
echo  J_family00 AI家人启动脚本
echo  您的专属夸夸官 💕
echo =====================================
echo.

:: 检查Python是否安装
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 未检测到Python，请先安装Python 3.8+
    pause
    exit /b 1
)

:: 检查Node.js是否安装
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 未检测到Node.js，请先安装Node.js 16+
    pause
    exit /b 1
)

:: 设置工作目录
cd /d "%~dp0"

:: 创建虚拟环境（如果不存在）
if not exist "venv" (
    echo [信息] 创建Python虚拟环境...
    python -m venv venv
)

:: 激活虚拟环境
echo [信息] 激活Python虚拟环境...
call venv\Scripts\activate.bat

:: 安装后端依赖
echo [信息] 安装后端依赖...
cd backend
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo [错误] 后端依赖安装失败
    pause
    exit /b 1
)

:: 创建环境配置文件
if not exist ".env" (
    echo [信息] 创建环境配置文件...
    copy .env.example .env
    echo [提示] 请根据需要修改 .env 文件中的配置
)

:: 初始化数据库
echo [信息] 初始化数据库...
python -c "from app.database.init_db import init_database; import asyncio; asyncio.run(init_database())"

:: 启动后端服务
echo [信息] 启动AI家人后端服务...
start cmd /k "title AI家人后端服务 && python main.py"

:: 等待后端启动
timeout /t 3 /nobreak >nul

:: 安装前端依赖
echo [信息] 安装前端依赖...
cd ..\frontend
if not exist "node_modules" (
    npm install
    if %errorlevel% neq 0 (
        echo [错误] 前端依赖安装失败
        pause
        exit /b 1
    )
)

:: 启动前端服务
echo [信息] 启动AI家人前端界面...
start cmd /k "title AI家人前端界面 && npm start"

echo.
echo =====================================
echo  🎉 AI家人启动完成！
echo  💕 前端界面: http://localhost:3000
echo  🔧 后端服务: http://localhost:8000
echo  📚 API文档: http://localhost:8000/docs
echo  您的专属夸夸官已经准备好为您服务了！
echo =====================================
echo.
echo 按任意键关闭此窗口...
pause >nul