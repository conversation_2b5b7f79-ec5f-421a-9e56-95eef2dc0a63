# J_family00 智能家庭助手 Makefile

.PHONY: help install start stop clean test lint format docker-build docker-run

# 默认目标
help:
	@echo "J_family00 智能家庭助手 - 可用命令："
	@echo ""
	@echo "  install     - 安装开发环境依赖"
	@echo "  start       - 启动开发服务器"
	@echo "  stop        - 停止开发服务器"
	@echo "  test        - 运行测试"
	@echo "  lint        - 代码质量检查"
	@echo "  format      - 代码格式化"
	@echo "  clean       - 清理临时文件"
	@echo "  docker-build - 构建Docker镜像"
	@echo "  docker-run  - 运行Docker容器"
	@echo ""

# 安装开发环境
install:
	@echo "安装开发环境..."
	@if [ ! -d "venv" ]; then python3 -m venv venv; fi
	@. venv/bin/activate && pip install --upgrade pip
	@. venv/bin/activate && cd backend && pip install -r requirements.txt
	@cd frontend && npm install
	@mkdir -p data/memories data/audio data/voices models logs
	@if [ ! -f "backend/.env" ]; then cp backend/.env.example backend/.env; fi
	@echo "开发环境安装完成！"

# 启动开发服务器
start:
	@echo "启动开发服务器..."
	@chmod +x start.sh
	@./start.sh

# 停止开发服务器
stop:
	@echo "停止开发服务器..."
	@chmod +x stop.sh
	@./stop.sh

# 运行测试
test:
	@echo "运行后端测试..."
	@. venv/bin/activate && cd backend && python -m pytest tests/ -v
	@echo "运行前端测试..."
	@cd frontend && npm test -- --watchAll=false

# 代码质量检查
lint:
	@echo "后端代码检查..."
	@. venv/bin/activate && cd backend && flake8 app/
	@echo "前端代码检查..."
	@cd frontend && npm run lint

# 代码格式化
format:
	@echo "格式化后端代码..."
	@. venv/bin/activate && cd backend && black app/
	@. venv/bin/activate && cd backend && isort app/
	@echo "格式化前端代码..."
	@cd frontend && npm run format

# 清理临时文件
clean:
	@echo "清理临时文件..."
	@find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
	@find . -type f -name "*.pyc" -delete 2>/dev/null || true
	@find . -type f -name "*.pyo" -delete 2>/dev/null || true
	@find . -type d -name ".pytest_cache" -exec rm -rf {} + 2>/dev/null || true
	@rm -f backend.pid frontend.pid
	@echo "清理完成！"

# 构建Docker镜像
docker-build:
	@echo "构建Docker镜像..."
	@docker build -t j_family00:latest .

# 运行Docker容器
docker-run:
	@echo "运行Docker容器..."
	@docker-compose up -d

# 查看Docker日志
docker-logs:
	@docker-compose logs -f

# 停止Docker容器
docker-stop:
	@docker-compose down

# 开发环境重置
reset:
	@echo "重置开发环境..."
	@make stop
	@make clean
	@rm -rf venv node_modules frontend/node_modules
	@rm -rf data/*.db logs/*.log
	@echo "重置完成！运行 'make install' 重新安装环境"