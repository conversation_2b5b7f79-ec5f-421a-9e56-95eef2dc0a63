"""
阿里百炼DashScope TTS语音合成服务
"""

import os
import uuid
import asyncio
from typing import Dict, Optional, Any, Tu<PERSON>
from pathlib import Path
import dashscope
from dashscope.audio.tts import SpeechSynthesizer
from app.core.config import settings
from app.core.logger import get_logger
from app.core.exceptions import ModelInitializationError, AudioProcessingError

logger = get_logger("tts")


class DashScopeTTSService:
    """阿里百炼TTS语音合成服务"""
    
    def __init__(self):
        self.api_key = settings.DASHSCOPE_API_KEY
        self.model_name = settings.TTS_MODEL_NAME
        self.voice = settings.TTS_VOICE
        self.speed = settings.TTS_SPEED
        self.pitch = settings.TTS_PITCH
        self.volume = settings.TTS_VOLUME
        self.output_dir = Path(settings.AUDIO_DIR) / "tts"
        self._initialize_service()
    
    def _initialize_service(self):
        """初始化TTS服务"""
        if not self.api_key:
            raise ModelInitializationError(
                "阿里百炼API密钥未配置，请在.env文件中设置DASHSCOPE_API_KEY",
                model_name="dashscope_tts"
            )
        
        # 设置API密钥
        dashscope.api_key = self.api_key
        
        # 确保输出目录存在
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"DashScope TTS服务初始化完成，模型: {self.model_name}, 语音: {self.voice}")
    
    async def synthesize_speech(
        self,
        text: str,
        voice: Optional[str] = None,
        speed: Optional[float] = None,
        pitch: Optional[float] = None,
        volume: Optional[float] = None,
        output_format: str = "wav"
    ) -> Dict[str, Any]:
        """
        合成语音
        
        Args:
            text: 要合成的文本
            voice: 语音类型
            speed: 语速 (0.5-2.0)
            pitch: 音调 (-500-500)
            volume: 音量 (0-100)
            output_format: 输出格式 (wav, mp3)
            
        Returns:
            包含音频文件路径和相关信息的字典
        """
        try:
            # 参数设置
            voice = voice or self.voice
            speed = speed or self.speed
            pitch = pitch or self.pitch
            volume = volume or self.volume
            
            # 验证参数
            if not text or not text.strip():
                raise AudioProcessingError("文本内容不能为空")
            
            if len(text) > 1000:
                raise AudioProcessingError("文本长度不能超过1000字符")
            
            # 生成输出文件名
            file_id = str(uuid.uuid4())[:8]
            output_filename = f"tts_{file_id}.{output_format}"
            output_path = self.output_dir / output_filename
            
            # 调用TTS API
            result = await self._call_tts_api(
                text=text,
                voice=voice,
                speed=speed,
                pitch=pitch,
                volume=volume,
                output_path=str(output_path)
            )
            
            if result["success"]:
                # 获取文件信息
                file_size = output_path.stat().st_size if output_path.exists() else 0
                
                logger.info(f"TTS合成成功: {output_filename}, 大小: {file_size} bytes")
                
                return {
                    "audio_path": str(output_path),
                    "filename": output_filename,
                    "file_size": file_size,
                    "duration": result.get("duration", 0),
                    "text": text,
                    "voice": voice,
                    "parameters": {
                        "speed": speed,
                        "pitch": pitch,
                        "volume": volume,
                        "format": output_format
                    },
                    "success": True
                }
            else:
                raise AudioProcessingError(f"TTS合成失败: {result.get('error', '未知错误')}")
                
        except Exception as e:
            logger.error(f"TTS语音合成失败: {str(e)}")
            raise AudioProcessingError(f"语音合成失败: {str(e)}")
    
    async def _call_tts_api(
        self,
        text: str,
        voice: str,
        speed: float,
        pitch: float,
        volume: float,
        output_path: str
    ) -> Dict[str, Any]:
        """调用TTS API"""
        try:
            # 在线程池中执行同步API调用
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                None,
                self._sync_call_tts,
                text, voice, speed, pitch, volume, output_path
            )
            
            return result
            
        except Exception as e:
            logger.error(f"TTS API调用失败: {str(e)}")
            return {"success": False, "error": str(e)}
    
    def _sync_call_tts(
        self,
        text: str,
        voice: str,
        speed: float,
        pitch: float,
        volume: float,
        output_path: str
    ) -> Dict[str, Any]:
        """同步调用TTS API"""
        try:
            # 构建TTS参数
            tts_params = {
                'model': self.model_name,
                'text': text,
                'voice': voice,
                'format': 'wav',
                'sample_rate': 16000,
                'volume': int(volume),
                'speech_rate': speed,
                'pitch_rate': pitch
            }
            
            # 调用语音合成
            response = SpeechSynthesizer.call(**tts_params)
            
            if response.get_audio_data() is not None:
                # 保存音频文件
                with open(output_path, 'wb') as f:
                    f.write(response.get_audio_data())
                
                return {
                    "success": True,
                    "duration": self._estimate_duration(text, speed)
                }
            else:
                return {
                    "success": False,
                    "error": f"TTS API返回空数据: {response.get_response()}"
                }
                
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def _estimate_duration(self, text: str, speed: float) -> float:
        """估算音频时长（秒）"""
        try:
            # 简单估算：中文平均每分钟200-300字
            chars_per_second = (250 / 60) * speed  # 考虑语速
            duration = len(text) / chars_per_second
            return round(duration, 2)
        except:
            return 0.0
    
    async def get_available_voices(self) -> Dict[str, Any]:
        """获取可用的语音列表"""
        # 阿里百炼支持的语音类型
        voices = {
            "zhichu": {
                "name": "知趣",
                "gender": "female",
                "language": "zh-CN",
                "description": "温暖亲切的女声"
            },
            "zhimiao": {
                "name": "知妙",
                "gender": "female", 
                "language": "zh-CN",
                "description": "清新自然的女声"
            },
            "zhixiang": {
                "name": "知向",
                "gender": "male",
                "language": "zh-CN",
                "description": "沉稳大气的男声"
            },
            "zhiyan": {
                "name": "知燕",
                "gender": "female",
                "language": "zh-CN",
                "description": "甜美可爱的女声"
            }
        }
        
        return {
            "voices": voices,
            "current_voice": self.voice,
            "model": self.model_name
        }
    
    async def cleanup_old_files(self, max_age_hours: int = 24) -> int:
        """清理旧的TTS文件"""
        try:
            import time
            current_time = time.time()
            max_age_seconds = max_age_hours * 3600
            deleted_count = 0
            
            for file_path in self.output_dir.glob("tts_*.wav"):
                if file_path.is_file():
                    file_age = current_time - file_path.stat().st_mtime
                    if file_age > max_age_seconds:
                        file_path.unlink()
                        deleted_count += 1
            
            logger.info(f"清理了 {deleted_count} 个旧的TTS文件")
            return deleted_count
            
        except Exception as e:
            logger.warning(f"清理TTS文件失败: {str(e)}")
            return 0
    
    async def get_service_info(self) -> Dict[str, Any]:
        """获取服务信息"""
        return {
            "model_name": self.model_name,
            "voice": self.voice,
            "parameters": {
                "speed": self.speed,
                "pitch": self.pitch,
                "volume": self.volume
            },
            "output_dir": str(self.output_dir),
            "api_configured": bool(self.api_key),
            "status": "ready"
        }
