"""
健康检查API测试
"""

import pytest
from httpx import AsyncClient


class TestHealthAPI:
    """健康检查API测试类"""
    
    @pytest.mark.asyncio
    async def test_root_endpoint(self, client: AsyncClient):
        """测试根路径端点"""
        response = await client.get("/")
        
        assert response.status_code == 200
        data = response.json()
        
        assert "message" in data
        assert "version" in data
        assert "status" in data
        assert data["status"] == "running"
        assert "AI家人" in data["message"]
    
    @pytest.mark.asyncio
    async def test_health_endpoint(self, client: AsyncClient):
        """测试健康检查端点"""
        response = await client.get("/health")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["status"] == "healthy"
        assert "AI家人" in data["message"]
    
    @pytest.mark.asyncio
    async def test_api_health_endpoint(self, client: AsyncClient):
        """测试API健康检查端点"""
        response = await client.get("/api/v1/health/")
        
        assert response.status_code == 200
        data = response.json()
        
        assert "status" in data
        assert "timestamp" in data
        assert "version" in data
        assert data["status"] == "healthy"
    
    @pytest.mark.asyncio
    async def test_api_system_info(self, client: AsyncClient):
        """测试系统信息端点"""
        response = await client.get("/api/v1/health/system")
        
        assert response.status_code == 200
        data = response.json()
        
        assert "system" in data
        assert "memory" in data
        assert "disk" in data
        assert "uptime" in data
