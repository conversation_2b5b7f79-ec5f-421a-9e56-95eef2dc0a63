"""
自定义中间件
"""

import time
import uuid
from typing import Callable
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from app.core.logger import get_api_logger

api_logger = get_api_logger()


class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """请求日志中间件"""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # 生成请求ID
        request_id = str(uuid.uuid4())[:8]
        
        # 获取客户端IP
        client_ip = request.client.host if request.client else "unknown"
        
        # 记录请求开始时间
        start_time = time.time()
        
        # 记录请求信息
        api_logger.info(
            f"Request started: {request.method} {request.url.path} "
            f"(IP: {client_ip}, ID: {request_id})"
        )
        
        # 处理请求
        try:
            response = await call_next(request)
            
            # 计算处理时间
            duration = time.time() - start_time
            
            # 记录响应信息
            api_logger.info(
                f"Request completed: {request.method} {request.url.path} - {response.status_code} "
                f"(Duration: {duration:.3f}s, IP: {client_ip}, ID: {request_id})"
            )
            
            # 添加响应头
            response.headers["X-Request-ID"] = request_id
            response.headers["X-Process-Time"] = f"{duration:.3f}"
            
            return response
            
        except Exception as e:
            # 计算处理时间
            duration = time.time() - start_time
            
            # 记录错误信息
            api_logger.error(
                f"Request failed: {request.method} {request.url.path} - {str(e)} "
                f"(Duration: {duration:.3f}s, IP: {client_ip}, ID: {request_id})"
            )
            
            # 重新抛出异常，让异常处理器处理
            raise


class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """安全头中间件"""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        response = await call_next(request)
        
        # 添加安全头
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        
        # 如果是HTTPS，添加HSTS头
        if request.url.scheme == "https":
            response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
        
        return response


class RateLimitMiddleware(BaseHTTPMiddleware):
    """简单的速率限制中间件"""
    
    def __init__(self, app, calls: int = 100, period: int = 60):
        super().__init__(app)
        self.calls = calls
        self.period = period
        self.clients = {}
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        client_ip = request.client.host if request.client else "unknown"
        current_time = time.time()
        
        # 清理过期的记录
        self.clients = {
            ip: timestamps for ip, timestamps in self.clients.items()
            if any(t > current_time - self.period for t in timestamps)
        }
        
        # 检查当前客户端的请求频率
        if client_ip in self.clients:
            # 过滤出时间窗口内的请求
            recent_requests = [
                t for t in self.clients[client_ip]
                if t > current_time - self.period
            ]
            
            if len(recent_requests) >= self.calls:
                from app.core.exceptions import RateLimitError
                raise RateLimitError("请求频率过高，请稍后再试")
            
            self.clients[client_ip] = recent_requests + [current_time]
        else:
            self.clients[client_ip] = [current_time]
        
        return await call_next(request)
