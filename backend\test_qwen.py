"""
测试Qwen API调用
"""

import asyncio
import os
from app.services.llm.qwen_service import QwenLLMService
from app.core.config import settings

async def test_qwen():
    print(f"API Key配置: {settings.DASHSCOPE_API_KEY[:10]}...")
    print(f"模型名称: {settings.QWEN_MODEL_NAME}")
    
    # 创建服务实例
    qwen_service = QwenLLMService()
    
    # 测试对话
    try:
        result = await qwen_service.generate_response(
            user_input="你好",
            member_id=1,
            member_name="测试用户"
        )
        
        print("API调用结果:")
        print(f"成功: {result['success']}")
        print(f"回复: {result['response']}")
        print(f"模型: {result['model']}")
        print(f"使用情况: {result['usage']}")
        
    except Exception as e:
        print(f"API调用失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_qwen())
