# 使用官方Python运行时作为父镜像
FROM python:3.9-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    portaudio19-dev \
    python3-pyaudio \
    ffmpeg \
    && rm -rf /var/lib/apt/lists/*

# 复制后端依赖文件
COPY backend/requirements.txt ./backend/

# 安装Python依赖
RUN pip install --no-cache-dir -r backend/requirements.txt

# 复制后端代码
COPY backend/ ./backend/

# 创建必要的目录
RUN mkdir -p data/memories data/audio data/voices models logs

# 设置环境变量
ENV PYTHONPATH=/app/backend
ENV DATA_DIR=/app/data
ENV MEMORIES_DIR=/app/data/memories
ENV AUDIO_DIR=/app/data/audio
ENV VOICES_DIR=/app/data/voices
ENV MODELS_DIR=/app/models
ENV LOGS_DIR=/app/logs

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["python", "backend/main.py"]