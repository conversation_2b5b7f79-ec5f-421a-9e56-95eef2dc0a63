"""
语音识别端点
"""

from fastapi import APIRouter, HTTPException, status, UploadFile, File
from typing import Optional, Dict, Any
from app.models.schemas import (
    VoiceRecognitionRequest,
    VoiceRecognitionResponse,
    VoiceProfileCreate,
    VoiceProfileResponse
)
from app.services.voice.speech_service import SpeechRecognitionService
from app.services.voice.voiceprint_service import VoiceprintService

router = APIRouter()


@router.post("/recognize", response_model=Dict[str, Any])
async def recognize_speech(
    audio_file: UploadFile = File(...),
    member_id: Optional[int] = None,
    language: Optional[str] = "zh"
):
    """语音识别和声纹识别"""
    try:
        # 验证音频文件
        if not audio_file.content_type or not audio_file.content_type.startswith('audio/'):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="请上传有效的音频文件"
            )
        
        # 创建语音识别服务
        speech_service = SpeechRecognitionService()
        
        # 保存上传的音频文件
        audio_path = await speech_service.save_audio_file(audio_file, member_id)
        
        # 验证音频文件
        validation_result = await speech_service.validate_audio_file(audio_path)
        if not validation_result["valid"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"音频文件验证失败: {', '.join(validation_result['issues'])}"
            )
        
        # 语音识别
        recognition_result = await speech_service.recognize_speech(
            audio_path=audio_path,
            language=language
        )
        
        # 声纹识别（如果没有指定成员ID）
        identified_member = None
        voice_confidence = 0.0
        if not member_id:
            try:
                voiceprint_service = VoiceprintService()
                identified_member, voice_confidence = await voiceprint_service.identify_speaker(audio_path)
            except Exception as e:
                print(f"声纹识别失败: {e}")
        
        # 构建响应
        response = {
            "speech_recognition": recognition_result,
            "voice_recognition": {
                "member_id": member_id or (identified_member.id if identified_member else None),
                "member_name": identified_member.name if identified_member else None,
                "confidence": voice_confidence
            },
            "audio_info": validation_result["info"],
            "audio_path": audio_path
        }
        
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"语音识别失败: {str(e)}"
        )


@router.post("/recognize/realtime")
async def recognize_realtime(
    audio_data: bytes,
    is_final: bool = False
):
    """实时语音识别"""
    try:
        speech_service = SpeechRecognitionService()
        result = await speech_service.recognize_realtime(
            audio_chunk=audio_data,
            is_final=is_final
        )
        return result
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"实时语音识别失败: {str(e)}"
        )


@router.get("/engines/info")
async def get_engine_info():
    """获取语音识别引擎信息"""
    try:
        speech_service = SpeechRecognitionService()
        engine_info = await speech_service.get_engine_info()
        supported_languages = await speech_service.get_supported_languages()
        
        return {
            "engine_info": engine_info,
            "supported_languages": supported_languages
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取引擎信息失败: {str(e)}"
        )


@router.post("/session/reset")
async def reset_realtime_session():
    """重置实时识别会话"""
    try:
        speech_service = SpeechRecognitionService()
        speech_service.reset_realtime_session()
        return {"message": "实时识别会话已重置"}
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"重置会话失败: {str(e)}"
        )


# 声纹识别相关端点（保持原有实现）
@router.post("/voiceprint/register", response_model=VoiceProfileResponse)
async def register_voiceprint(
    member_id: int,
    audio_file: UploadFile = File(...)
):
    """注册声纹"""
    try:
        voiceprint_service = VoiceprintService()
        
        # 保存音频文件
        audio_path = await voiceprint_service.save_audio_file(audio_file)
        
        # 创建声纹特征
        voice_profile = await voiceprint_service.create_voice_profile(
            member_id, audio_path
        )
        
        return voice_profile
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"声纹注册失败: {str(e)}"
        )


@router.put("/voiceprint/{member_id}", response_model=VoiceProfileResponse)
async def update_voiceprint(
    member_id: int,
    audio_file: UploadFile = File(...)
):
    """更新声纹"""
    try:
        voiceprint_service = VoiceprintService()
        
        # 保存音频文件
        audio_path = await voiceprint_service.save_audio_file(audio_file)
        
        # 更新声纹特征
        voice_profile = await voiceprint_service.update_voice_profile(
            member_id, audio_path
        )
        
        return voice_profile
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"声纹更新失败: {str(e)}"
        )


@router.get("/voiceprint/{member_id}", response_model=VoiceProfileResponse)
async def get_voiceprint(
    member_id: int
):
    """获取声纹信息"""
    voiceprint_service = VoiceprintService()
    voice_profile = await voiceprint_service.get_voice_profile(member_id)
    if not voice_profile:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="声纹信息不存在"
        )
    return voice_profile


@router.delete("/voiceprint/{member_id}")
async def delete_voiceprint(
    member_id: int
):
    """删除声纹"""
    try:
        voiceprint_service = VoiceprintService()
        success = await voiceprint_service.delete_voice_profile(member_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="声纹信息不存在"
            )
        return {"message": "声纹删除成功"}
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"删除声纹失败: {str(e)}"
        )