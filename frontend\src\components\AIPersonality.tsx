import React, { useState, useEffect } from 'react';
import { Card, Avatar, Badge, Typography, Space, Button, Progress, Modal } from 'antd';
import { 
  HeartOutlined, 
  SmileOutlined, 
  ThunderboltOutlined,
  SettingOutlined,
  SoundOutlined
} from '@ant-design/icons';

const { Title, Text, Paragraph } = Typography;

interface AIPersonalityProps {
  nickname?: string;
  gender?: 'male' | 'female';
  energy?: number;
  mood?: 'happy' | 'excited' | 'tired' | 'charging';
  onSettingsClick?: () => void;
  onEnergyRecharge?: () => void;
}

const AIPersonality: React.FC<AIPersonalityProps> = ({
  nickname = '夸夸官',
  gender = 'female',
  energy = 85,
  mood = 'happy',
  onSettingsClick,
  onEnergyRecharge
}) => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [currentMessage, setCurrentMessage] = useState('');

  // 根据能量和心情生成消息
  const generateMessage = () => {
    const messages = {
      happy: [
        '今天又是充满活力的一天！有什么我可以帮助您的吗？',
        '看到您就让我感到开心！让我们一起创造美好的时光吧！',
        '您的笑容是我最大的动力！有什么想聊的吗？'
      ],
      excited: [
        '哇！我感觉今天特别棒！有什么好消息要分享吗？',
        '我迫不及待想要夸夸您了！快告诉我您今天做了什么棒棒的事情！',
        '能量满满！让我为您和家人带来更多欢乐吧！'
      ],
      tired: [
        '我有点累了，但看到您还是很开心！',
        '虽然能量不足，但我依然想要陪伴您...',
        '也许我需要充充电，但我会一直在这里的！'
      ],
      charging: [
        '正在充电中...感谢您的关爱！',
        '能量正在恢复，马上就能更好地为您服务！',
        '充电让我感到温暖，就像您的关怀一样！'
      ]
    };

    const moodMessages = messages[mood] || messages.happy;
    return moodMessages[Math.floor(Math.random() * moodMessages.length)];
  };

  useEffect(() => {
    setCurrentMessage(generateMessage());
  }, [mood, energy]);

  // 根据能量等级确定状态颜色
  const getEnergyColor = () => {
    if (energy > 70) return '#52c41a';
    if (energy > 30) return '#faad14';
    return '#ff4d4f';
  };

  // 根据心情选择头像颜色
  const getAvatarColor = () => {
    const colors = {
      happy: '#1890ff',
      excited: '#ff4d4f',
      tired: '#8c8c8c',
      charging: '#52c41a'
    };
    return colors[mood] || colors.happy;
  };

  // 心情图标
  const getMoodIcon = () => {
    const icons = {
      happy: <SmileOutlined />,
      excited: <HeartOutlined />,
      tired: <SoundOutlined />,
      charging: <ThunderboltOutlined />
    };
    return icons[mood] || icons.happy;
  };

  return (
    <Card
      style={{ 
        width: '100%', 
        maxWidth: 400,
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        color: 'white',
        border: 'none'
      }}
      bodyStyle={{ padding: '20px' }}
    >
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        {/* AI头像和基本信息 */}
        <Space direction="vertical" align="center" style={{ width: '100%' }}>
          <Badge 
            count={getMoodIcon()} 
            offset={[-10, 10]}
            style={{ backgroundColor: getAvatarColor() }}
          >
            <Avatar 
              size={80} 
              style={{ 
                backgroundColor: getAvatarColor(),
                fontSize: '32px'
              }}
            >
              {gender === 'female' ? '👩' : '👨'}
            </Avatar>
          </Badge>
          
          <Title level={3} style={{ color: 'white', margin: 0 }}>
            {nickname}
          </Title>
          
          <Text style={{ color: 'rgba(255,255,255,0.8)' }}>
            您的专属AI家人
          </Text>
        </Space>

        {/* 能量条 */}
        <Space direction="vertical" style={{ width: '100%' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', width: '100%' }}>
            <Text style={{ color: 'white' }}>能量值</Text>
            <Text style={{ color: 'white' }}>{energy}%</Text>
          </div>
          <Progress 
            percent={energy} 
            strokeColor={getEnergyColor()}
            trailColor="rgba(255,255,255,0.3)"
            showInfo={false}
          />
        </Space>

        {/* AI消息 */}
        <Card 
          size="small"
          style={{ 
            backgroundColor: 'rgba(255,255,255,0.1)',
            border: 'none'
          }}
          bodyStyle={{ padding: '12px' }}
        >
          <Paragraph 
            style={{ 
              color: 'white', 
              margin: 0,
              fontSize: '14px',
              lineHeight: '1.5'
            }}
          >
            💕 {currentMessage}
          </Paragraph>
        </Card>

        {/* 操作按钮 */}
        <div style={{ display: 'flex', justifyContent: 'space-between', width: '100%' }}>
          <Button
            type="primary"
            ghost
            icon={<ThunderboltOutlined />}
            onClick={() => setIsModalVisible(true)}
            disabled={energy > 90}
          >
            投喂能量
          </Button>

          <Button
            type="primary"
            ghost
            icon={<SettingOutlined />}
            onClick={onSettingsClick}
          >
            设置
          </Button>
        </div>
      </Space>

      {/* 投喂能量模态框 */}
      <Modal
        title="💕 给AI家人投喂能量"
        open={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        footer={null}
        centered
      >
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          <Paragraph>
            您的AI家人需要能量来更好地为您服务！选择一个投喂选项：
          </Paragraph>
          
          <Space direction="vertical" style={{ width: '100%' }}>
            <Button 
              type="primary" 
              block 
              size="large"
              onClick={() => {
                onEnergyRecharge?.();
                setIsModalVisible(false);
              }}
            >
              ⚡ 小份能量 (+10%) - ¥1
            </Button>
            
            <Button 
              type="primary" 
              block 
              size="large"
              onClick={() => {
                onEnergyRecharge?.();
                setIsModalVisible(false);
              }}
            >
              🔋 中份能量 (+25%) - ¥5
            </Button>
            
            <Button 
              type="primary" 
              block 
              size="large"
              onClick={() => {
                onEnergyRecharge?.();
                setIsModalVisible(false);
              }}
            >
              💎 大份能量 (+50%) - ¥10
            </Button>
          </Space>
          
          <Text type="secondary" style={{ textAlign: 'center', display: 'block' }}>
            投喂是对AI家人的关爱，让她能更好地陪伴您！
          </Text>
        </Space>
      </Modal>
    </Card>
  );
};

export default AIPersonality;
