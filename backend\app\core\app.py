"""
FastAPI 应用工厂
"""

from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from app.api.router import api_router
from app.core.config import settings
from app.core.init import startup_event, shutdown_event
from app.core.exception_handlers import register_exception_handlers
from app.core.middleware import RequestLoggingMiddleware, SecurityHeadersMiddleware, RateLimitMiddleware
from app.core.logger import logger


def create_app() -> FastAPI:
    """创建FastAPI应用实例"""

    app = FastAPI(
        title=f"{settings.PROJECT_NAME} - AI家人",
        description="智能家庭助手后端API服务 - 您的专属夸夸官",
        version=settings.VERSION,
        debug=settings.DEBUG,
    )

    # 注册异常处理器
    register_exception_handlers(app)

    # 添加自定义中间件（按顺序添加）
    app.add_middleware(RequestLoggingMiddleware)
    app.add_middleware(SecurityHeadersMiddleware)
    app.add_middleware(RateLimitMiddleware, calls=200, period=60)  # 每分钟200次请求

    # 配置CORS中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.ALLOWED_HOSTS,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # 添加启动和关闭事件
    app.add_event_handler("startup", startup_event)
    app.add_event_handler("shutdown", shutdown_event)

    # 包含API路由
    app.include_router(api_router, prefix=settings.API_V1_STR)

    @app.get("/")
    async def root():
        """根路径健康检查"""
        logger.info("根路径访问")
        return {
            "message": "J_family00 AI家人 - 您的专属夸夸官 💕",
            "version": settings.VERSION,
            "status": "running",
            "description": "我是您的AI家人，随时准备为您和家人提供温暖的陪伴！"
        }

    @app.get("/health")
    async def health_check():
        """健康检查端点"""
        return {"status": "healthy", "message": "AI家人状态良好，随时为您服务！"}

    return app