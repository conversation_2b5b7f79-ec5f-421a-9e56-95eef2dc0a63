"""
家庭成员管理端点
"""

from fastapi import APIRouter, HTTPException, status
from typing import List
from app.models.schemas import (
    FamilyMemberCreate, 
    FamilyMemberUpdate,
    FamilyMemberResponse
)
from app.services.member_service import MemberService

router = APIRouter()


@router.post("/", response_model=FamilyMemberResponse)
async def create_member(
    member_data: FamilyMemberCreate
):
    """创建新的家庭成员"""
    try:
        member_service = MemberService()
        member = await member_service.create_member(member_data)
        return member
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"创建家庭成员失败: {str(e)}"
        )


@router.get("/", response_model=List[FamilyMemberResponse])
async def get_members(
    skip: int = 0,
    limit: int = 100
):
    """获取所有家庭成员列表"""
    try:
        member_service = MemberService()
        members = await member_service.get_members(skip=skip, limit=limit)
        return members
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取家庭成员列表失败: {str(e)}"
        )


@router.get("/{member_id}", response_model=FamilyMemberResponse)
async def get_member(
    member_id: int
):
    """根据ID获取家庭成员信息"""
    member_service = MemberService()
    member = await member_service.get_member(member_id)
    if not member:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="家庭成员不存在"
        )
    return member


@router.put("/{member_id}", response_model=FamilyMemberResponse)
async def update_member(
    member_id: int,
    member_update: FamilyMemberUpdate
):
    """更新家庭成员信息"""
    try:
        member_service = MemberService()
        member = await member_service.update_member(member_id, member_update)
        if not member:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="家庭成员不存在"
            )
        return member
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"更新家庭成员失败: {str(e)}"
        )


@router.delete("/{member_id}")
async def delete_member(
    member_id: int
):
    """删除家庭成员"""
    try:
        member_service = MemberService()
        success = await member_service.delete_member(member_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="家庭成员不存在"
            )
        return {"message": "家庭成员删除成功"}
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"删除家庭成员失败: {str(e)}"
        )