"""
记忆管理端点
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from typing import List, Optional, Dict, Any
from app.models.schemas import (
    MemoryEntry,
    MemoryCreate,
    MemoryUpdate,
    MemorySearchRequest,
    MemoryResponse
)
from app.services.memory.memory_service import MemoryService

router = APIRouter()


@router.post("/{member_id}/add", response_model=MemoryResponse)
async def add_memory(
    member_id: int,
    memory_data: MemoryCreate,
    memory_service: MemoryService = Depends()
):
    """为指定成员添加记忆"""
    try:
        memory = await memory_service.add_memory(member_id, memory_data)
        return memory
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"添加记忆失败: {str(e)}"
        )


@router.get("/{member_id}/search", response_model=List[MemoryResponse])
async def search_memories(
    member_id: int,
    query: str = Query(..., description="搜索关键词"),
    memory_type: Optional[str] = Query(None, description="记忆类型"),
    limit: int = Query(10, description="返回数量限制"),
    memory_service: MemoryService = Depends()
):
    """搜索指定成员的记忆"""
    try:
        memories = await memory_service.search_memories(
            member_id=member_id,
            query=query,
            memory_type=memory_type,
            limit=limit
        )
        return memories
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"搜索记忆失败: {str(e)}"
        )


@router.get("/{member_id}/all", response_model=List[MemoryResponse])
async def get_member_memories(
    member_id: int,
    memory_type: Optional[str] = Query(None, description="记忆类型"),
    skip: int = Query(0, description="跳过数量"),
    limit: int = Query(50, description="返回数量限制"),
    memory_service: MemoryService = Depends()
):
    """获取指定成员的所有记忆"""
    try:
        memories = await memory_service.get_member_memories(
            member_id=member_id,
            memory_type=memory_type,
            skip=skip,
            limit=limit
        )
        return memories
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取记忆失败: {str(e)}"
        )


@router.put("/{member_id}/memory/{memory_id}", response_model=MemoryResponse)
async def update_memory(
    member_id: int,
    memory_id: str,
    memory_update: MemoryUpdate,
    memory_service: MemoryService = Depends()
):
    """更新指定记忆"""
    try:
        memory = await memory_service.update_memory(
            member_id, memory_id, memory_update
        )
        if not memory:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="记忆不存在"
            )
        return memory
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"更新记忆失败: {str(e)}"
        )


@router.delete("/{member_id}/memory/{memory_id}")
async def delete_memory(
    member_id: int,
    memory_id: str,
    memory_service: MemoryService = Depends()
):
    """删除指定记忆"""
    try:
        success = await memory_service.delete_memory(member_id, memory_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="记忆不存在"
            )
        return {"message": "记忆删除成功"}
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"删除记忆失败: {str(e)}"
        )


@router.get("/{member_id}/stats")
async def get_memory_stats(
    member_id: int,
    memory_service: MemoryService = Depends()
):
    """获取指定成员的记忆统计信息"""
    try:
        stats = await memory_service.get_memory_stats(member_id)
        return stats
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取记忆统计失败: {str(e)}"
        )


@router.post("/{member_id}/backup")
async def backup_memories(
    member_id: int,
    memory_service: MemoryService = Depends()
):
    """备份指定成员的记忆"""
    try:
        backup_path = await memory_service.backup_memories(member_id)
        return {
            "message": "记忆备份成功",
            "backup_path": backup_path
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"备份记忆失败: {str(e)}"
        )