"""
JSON记忆存储基础类
"""

import json
import os
import uuid
from typing import Dict, List, Optional, Any
from datetime import datetime
from pathlib import Path
import aiofiles
from jsonschema import validate, ValidationError
from app.core.config import settings


class JSONMemoryStore:
    """JSON记忆存储基础类"""
    
    # JSO<PERSON> Schema for memory validation
    MEMORY_SCHEMA = {
        "type": "object",
        "properties": {
            "member_id": {"type": "integer"},
            "member_name": {"type": "string"},
            "memories": {
                "type": "object",
                "properties": {
                    "preferences": {"type": "object"},
                    "conversations": {"type": "array"},
                    "events": {"type": "array"},
                    "habits": {"type": "object"},
                    "reminders": {"type": "array"}
                }
            },
            "metadata": {
                "type": "object",
                "properties": {
                    "created_at": {"type": "string"},
                    "updated_at": {"type": "string"},
                    "version": {"type": "string"},
                    "total_entries": {"type": "integer"}
                }
            }
        },
        "required": ["member_id", "member_name", "memories", "metadata"]
    }
    
    def __init__(self):
        self.memories_dir = Path(settings.MEMORIES_DIR)
        self.memories_dir.mkdir(parents=True, exist_ok=True)
    
    def _get_file_path(self, member_id: int, member_name: str) -> Path:
        """获取成员记忆文件路径"""
        filename = f"member_{member_id}_{member_name}.json"
        return self.memories_dir / filename
    
    def _create_empty_memory_structure(self, member_id: int, member_name: str) -> Dict[str, Any]:
        """创建空的记忆结构"""
        now = datetime.utcnow().isoformat()
        return {
            "member_id": member_id,
            "member_name": member_name,
            "memories": {
                "preferences": {
                    "favorite_topics": [],
                    "communication_style": "default",
                    "interests": [],
                    "dislikes": []
                },
                "conversations": [],
                "events": [],
                "habits": {
                    "daily_routines": [],
                    "behavioral_patterns": []
                },
                "reminders": []
            },
            "metadata": {
                "created_at": now,
                "updated_at": now,
                "version": "1.0.0",
                "total_entries": 0
            }
        }
    
    async def _load_memory_file(self, file_path: Path) -> Optional[Dict[str, Any]]:
        """加载记忆文件"""
        try:
            if not file_path.exists():
                return None
            
            async with aiofiles.open(file_path, 'r', encoding='utf-8') as f:
                content = await f.read()
                data = json.loads(content)
                
            # 验证JSON结构
            validate(data, self.MEMORY_SCHEMA)
            return data
            
        except (json.JSONDecodeError, ValidationError, Exception) as e:
            print(f"Error loading memory file {file_path}: {e}")
            return None
    
    async def _save_memory_file(self, file_path: Path, data: Dict[str, Any]) -> bool:
        """保存记忆文件"""
        try:
            # 验证数据结构
            validate(data, self.MEMORY_SCHEMA)
            
            # 更新元数据
            data["metadata"]["updated_at"] = datetime.utcnow().isoformat()
            data["metadata"]["total_entries"] = self._count_total_entries(data)
            
            # 保存文件
            async with aiofiles.open(file_path, 'w', encoding='utf-8') as f:
                await f.write(json.dumps(data, ensure_ascii=False, indent=2))
            
            return True
            
        except (ValidationError, Exception) as e:
            print(f"Error saving memory file {file_path}: {e}")
            return False
    
    def _count_total_entries(self, data: Dict[str, Any]) -> int:
        """计算总记忆条目数"""
        total = 0
        memories = data.get("memories", {})
        
        # 计算各类型记忆数量
        total += len(memories.get("conversations", []))
        total += len(memories.get("events", []))
        total += len(memories.get("reminders", []))
        
        # 计算偏好和习惯条目数
        preferences = memories.get("preferences", {})
        for key, value in preferences.items():
            if isinstance(value, list):
                total += len(value)
            elif value:
                total += 1
        
        habits = memories.get("habits", {})
        for key, value in habits.items():
            if isinstance(value, list):
                total += len(value)
        
        return total
    
    async def create_memory_file(self, member_id: int, member_name: str) -> bool:
        """为新成员创建记忆文件"""
        file_path = self._get_file_path(member_id, member_name)
        
        if file_path.exists():
            return False  # 文件已存在
        
        data = self._create_empty_memory_structure(member_id, member_name)
        return await self._save_memory_file(file_path, data)
    
    async def get_member_memories(self, member_id: int, member_name: str) -> Optional[Dict[str, Any]]:
        """获取成员的所有记忆"""
        file_path = self._get_file_path(member_id, member_name)
        return await self._load_memory_file(file_path)
    
    async def memory_file_exists(self, member_id: int, member_name: str) -> bool:
        """检查记忆文件是否存在"""
        file_path = self._get_file_path(member_id, member_name)
        return file_path.exists()
    
    async def backup_memory_file(self, member_id: int, member_name: str) -> Optional[str]:
        """备份记忆文件"""
        try:
            source_path = self._get_file_path(member_id, member_name)
            if not source_path.exists():
                return None
            
            # 创建备份文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"member_{member_id}_{member_name}_backup_{timestamp}.json"
            backup_path = self.memories_dir / "backups" / backup_filename
            
            # 确保备份目录存在
            backup_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 复制文件
            async with aiofiles.open(source_path, 'r', encoding='utf-8') as source:
                content = await source.read()
            
            async with aiofiles.open(backup_path, 'w', encoding='utf-8') as backup:
                await backup.write(content)
            
            return str(backup_path)
            
        except Exception as e:
            print(f"Error backing up memory file: {e}")
            return None
    
    async def delete_memory_file(self, member_id: int, member_name: str) -> bool:
        """删除记忆文件"""
        try:
            file_path = self._get_file_path(member_id, member_name)
            if file_path.exists():
                # 先备份再删除
                await self.backup_memory_file(member_id, member_name)
                file_path.unlink()
                return True
            return False
        except Exception as e:
            print(f"Error deleting memory file: {e}")
            return False
    
    async def get_memory_stats(self, member_id: int, member_name: str) -> Optional[Dict[str, Any]]:
        """获取记忆文件统计信息"""
        try:
            file_path = self._get_file_path(member_id, member_name)
            if not file_path.exists():
                return None
            
            data = await self._load_memory_file(file_path)
            if not data:
                return None
            
            stat = file_path.stat()
            memories = data.get("memories", {})
            
            return {
                "file_size": stat.st_size,
                "created_at": data["metadata"]["created_at"],
                "updated_at": data["metadata"]["updated_at"],
                "version": data["metadata"]["version"],
                "total_entries": data["metadata"]["total_entries"],
                "conversation_count": len(memories.get("conversations", [])),
                "event_count": len(memories.get("events", [])),
                "reminder_count": len(memories.get("reminders", [])),
                "preference_count": len([v for v in memories.get("preferences", {}).values() if v]),
                "habit_count": sum(len(v) if isinstance(v, list) else 1 
                                 for v in memories.get("habits", {}).values())
            }
            
        except Exception as e:
            print(f"Error getting memory stats: {e}")
            return None