"""
自定义异常类和错误处理
"""

from typing import Any, Dict, Optional
from fastapi import HTTPException, status


class BaseCustomException(Exception):
    """基础自定义异常类"""
    
    def __init__(
        self,
        message: str,
        error_code: str = "UNKNOWN_ERROR",
        details: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        super().__init__(self.message)


class ValidationError(BaseCustomException):
    """数据验证错误"""
    
    def __init__(self, message: str, field: str = None, details: Dict[str, Any] = None):
        super().__init__(
            message=message,
            error_code="VALIDATION_ERROR",
            details={"field": field, **(details or {})}
        )


class DatabaseError(BaseCustomException):
    """数据库操作错误"""
    
    def __init__(self, message: str, operation: str = None, details: Dict[str, Any] = None):
        super().__init__(
            message=message,
            error_code="DATABASE_ERROR",
            details={"operation": operation, **(details or {})}
        )


class AudioProcessingError(BaseCustomException):
    """音频处理错误"""
    
    def __init__(self, message: str, audio_path: str = None, details: Dict[str, Any] = None):
        super().__init__(
            message=message,
            error_code="AUDIO_PROCESSING_ERROR",
            details={"audio_path": audio_path, **(details or {})}
        )


class SpeechRecognitionError(BaseCustomException):
    """语音识别错误"""
    
    def __init__(self, message: str, engine: str = None, details: Dict[str, Any] = None):
        super().__init__(
            message=message,
            error_code="SPEECH_RECOGNITION_ERROR",
            details={"engine": engine, **(details or {})}
        )


class VoiceprintError(BaseCustomException):
    """声纹识别错误"""
    
    def __init__(self, message: str, member_id: int = None, details: Dict[str, Any] = None):
        super().__init__(
            message=message,
            error_code="VOICEPRINT_ERROR",
            details={"member_id": member_id, **(details or {})}
        )


class MemoryError(BaseCustomException):
    """记忆管理错误"""
    
    def __init__(self, message: str, member_id: int = None, details: Dict[str, Any] = None):
        super().__init__(
            message=message,
            error_code="MEMORY_ERROR",
            details={"member_id": member_id, **(details or {})}
        )


class ConfigurationError(BaseCustomException):
    """配置错误"""
    
    def __init__(self, message: str, config_key: str = None, details: Dict[str, Any] = None):
        super().__init__(
            message=message,
            error_code="CONFIGURATION_ERROR",
            details={"config_key": config_key, **(details or {})}
        )


class ModelInitializationError(BaseCustomException):
    """模型初始化错误"""
    
    def __init__(self, message: str, model_name: str = None, details: Dict[str, Any] = None):
        super().__init__(
            message=message,
            error_code="MODEL_INITIALIZATION_ERROR",
            details={"model_name": model_name, **(details or {})}
        )


# HTTP异常映射
def create_http_exception(exc: BaseCustomException) -> HTTPException:
    """将自定义异常转换为HTTP异常"""
    
    status_code_map = {
        "VALIDATION_ERROR": status.HTTP_400_BAD_REQUEST,
        "DATABASE_ERROR": status.HTTP_500_INTERNAL_SERVER_ERROR,
        "AUDIO_PROCESSING_ERROR": status.HTTP_400_BAD_REQUEST,
        "SPEECH_RECOGNITION_ERROR": status.HTTP_500_INTERNAL_SERVER_ERROR,
        "VOICEPRINT_ERROR": status.HTTP_500_INTERNAL_SERVER_ERROR,
        "MEMORY_ERROR": status.HTTP_500_INTERNAL_SERVER_ERROR,
        "CONFIGURATION_ERROR": status.HTTP_500_INTERNAL_SERVER_ERROR,
        "MODEL_INITIALIZATION_ERROR": status.HTTP_500_INTERNAL_SERVER_ERROR,
        "UNKNOWN_ERROR": status.HTTP_500_INTERNAL_SERVER_ERROR,
    }
    
    status_code = status_code_map.get(exc.error_code, status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    return HTTPException(
        status_code=status_code,
        detail={
            "error_code": exc.error_code,
            "message": exc.message,
            "details": exc.details
        }
    )


# 常用的HTTP异常
class NotFoundError(HTTPException):
    """资源未找到错误"""
    
    def __init__(self, resource: str, identifier: Any = None):
        detail = f"{resource} not found"
        if identifier:
            detail += f" (ID: {identifier})"
        
        super().__init__(
            status_code=status.HTTP_404_NOT_FOUND,
            detail={
                "error_code": "RESOURCE_NOT_FOUND",
                "message": detail,
                "details": {"resource": resource, "identifier": identifier}
            }
        )


class ConflictError(HTTPException):
    """资源冲突错误"""
    
    def __init__(self, resource: str, message: str = None):
        detail = message or f"{resource} already exists"
        
        super().__init__(
            status_code=status.HTTP_409_CONFLICT,
            detail={
                "error_code": "RESOURCE_CONFLICT",
                "message": detail,
                "details": {"resource": resource}
            }
        )


class UnauthorizedError(HTTPException):
    """未授权错误"""
    
    def __init__(self, message: str = "Unauthorized access"):
        super().__init__(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail={
                "error_code": "UNAUTHORIZED",
                "message": message,
                "details": {}
            }
        )


class ForbiddenError(HTTPException):
    """禁止访问错误"""
    
    def __init__(self, message: str = "Access forbidden"):
        super().__init__(
            status_code=status.HTTP_403_FORBIDDEN,
            detail={
                "error_code": "FORBIDDEN",
                "message": message,
                "details": {}
            }
        )


class RateLimitError(HTTPException):
    """请求频率限制错误"""
    
    def __init__(self, message: str = "Rate limit exceeded"):
        super().__init__(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail={
                "error_code": "RATE_LIMIT_EXCEEDED",
                "message": message,
                "details": {}
            }
        )
