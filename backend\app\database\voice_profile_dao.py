"""
声纹数据访问对象 (DAO)
"""

from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete
from app.models.database import VoiceProfile


class VoiceProfileDAO:
    """声纹数据访问对象"""
    
    def __init__(self, session: AsyncSession):
        self.session = session
    
    async def create(
        self, 
        member_id: int, 
        profile_data: str, 
        model_version: str
    ) -> VoiceProfile:
        """创建声纹记录"""
        voice_profile = VoiceProfile(
            member_id=member_id,
            profile_data=profile_data,
            model_version=model_version,
            sample_count=1
        )
        
        self.session.add(voice_profile)
        await self.session.commit()
        await self.session.refresh(voice_profile)
        return voice_profile
    
    async def get_by_id(self, profile_id: int) -> Optional[VoiceProfile]:
        """根据ID获取声纹记录"""
        result = await self.session.execute(
            select(VoiceProfile)
            .where(VoiceProfile.id == profile_id)
        )
        return result.scalar_one_or_none()
    
    async def get_by_member(self, member_id: int) -> Optional[VoiceProfile]:
        """根据成员ID获取声纹记录"""
        result = await self.session.execute(
            select(VoiceProfile)
            .where(VoiceProfile.member_id == member_id)
            .where(VoiceProfile.is_active == True)
            .order_by(VoiceProfile.updated_at.desc())
        )
        return result.scalar_one_or_none()
    
    async def get_all_active(self) -> List[VoiceProfile]:
        """获取所有活跃的声纹记录"""
        result = await self.session.execute(
            select(VoiceProfile)
            .where(VoiceProfile.is_active == True)
            .order_by(VoiceProfile.updated_at.desc())
        )
        return result.scalars().all()
    
    async def update_profile_data(
        self, 
        member_id: int, 
        profile_data: str, 
        model_version: str
    ) -> Optional[VoiceProfile]:
        """更新声纹数据"""
        # 先获取现有记录
        existing_profile = await self.get_by_member(member_id)
        
        if existing_profile:
            # 更新现有记录
            await self.session.execute(
                update(VoiceProfile)
                .where(VoiceProfile.member_id == member_id)
                .where(VoiceProfile.is_active == True)
                .values(
                    profile_data=profile_data,
                    model_version=model_version,
                    sample_count=existing_profile.sample_count + 1
                )
            )
            await self.session.commit()
            return await self.get_by_member(member_id)
        else:
            # 创建新记录
            return await self.create(member_id, profile_data, model_version)
    
    async def increment_sample_count(self, member_id: int) -> bool:
        """增加样本数量"""
        result = await self.session.execute(
            update(VoiceProfile)
            .where(VoiceProfile.member_id == member_id)
            .where(VoiceProfile.is_active == True)
            .values(sample_count=VoiceProfile.sample_count + 1)
        )
        await self.session.commit()
        return result.rowcount > 0
    
    async def deactivate(self, member_id: int) -> bool:
        """停用声纹记录"""
        result = await self.session.execute(
            update(VoiceProfile)
            .where(VoiceProfile.member_id == member_id)
            .values(is_active=False)
        )
        await self.session.commit()
        return result.rowcount > 0
    
    async def delete(self, member_id: int) -> bool:
        """删除声纹记录"""
        result = await self.session.execute(
            delete(VoiceProfile)
            .where(VoiceProfile.member_id == member_id)
        )
        await self.session.commit()
        return result.rowcount > 0
    
    async def get_all_by_model_version(self, model_version: str) -> List[VoiceProfile]:
        """根据模型版本获取声纹记录"""
        result = await self.session.execute(
            select(VoiceProfile)
            .where(VoiceProfile.model_version == model_version)
            .where(VoiceProfile.is_active == True)
        )
        return result.scalars().all()
    
    async def get_profile_count(self) -> int:
        """获取活跃声纹记录数量"""
        result = await self.session.execute(
            select(VoiceProfile)
            .where(VoiceProfile.is_active == True)
        )
        return len(result.scalars().all())