"""
音频文件数据访问对象 (DAO)
"""

from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, delete
from app.models.database import AudioFile


class AudioFileDAO:
    """音频文件数据访问对象"""
    
    def __init__(self, session: AsyncSession):
        self.session = session
    
    async def create(
        self,
        file_path: str,
        file_name: str,
        member_id: Optional[int] = None,
        file_size: Optional[int] = None,
        duration: Optional[float] = None,
        sample_rate: Optional[int] = None,
        channels: Optional[int] = None,
        format: Optional[str] = None,
        file_type: str = "input"
    ) -> AudioFile:
        """创建音频文件记录"""
        audio_file = AudioFile(
            member_id=member_id,
            file_path=file_path,
            file_name=file_name,
            file_size=file_size,
            duration=duration,
            sample_rate=sample_rate,
            channels=channels,
            format=format,
            file_type=file_type
        )
        
        self.session.add(audio_file)
        await self.session.commit()
        await self.session.refresh(audio_file)
        return audio_file
    
    async def get_by_id(self, file_id: int) -> Optional[AudioFile]:
        """根据ID获取音频文件记录"""
        result = await self.session.execute(
            select(AudioFile)
            .where(AudioFile.id == file_id)
        )
        return result.scalar_one_or_none()
    
    async def get_by_path(self, file_path: str) -> Optional[AudioFile]:
        """根据文件路径获取音频文件记录"""
        result = await self.session.execute(
            select(AudioFile)
            .where(AudioFile.file_path == file_path)
        )
        return result.scalar_one_or_none()
    
    async def get_by_member(
        self, 
        member_id: int,
        file_type: Optional[str] = None,
        skip: int = 0,
        limit: int = 50
    ) -> List[AudioFile]:
        """获取指定成员的音频文件记录"""
        query = select(AudioFile).where(AudioFile.member_id == member_id)
        
        if file_type:
            query = query.where(AudioFile.file_type == file_type)
        
        query = query.offset(skip).limit(limit).order_by(AudioFile.created_at.desc())
        
        result = await self.session.execute(query)
        return result.scalars().all()
    
    async def get_all(
        self,
        file_type: Optional[str] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[AudioFile]:
        """获取所有音频文件记录"""
        query = select(AudioFile)
        
        if file_type:
            query = query.where(AudioFile.file_type == file_type)
        
        query = query.offset(skip).limit(limit).order_by(AudioFile.created_at.desc())
        
        result = await self.session.execute(query)
        return result.scalars().all()
    
    async def delete(self, file_id: int) -> bool:
        """删除音频文件记录"""
        result = await self.session.execute(
            delete(AudioFile)
            .where(AudioFile.id == file_id)
        )
        await self.session.commit()
        return result.rowcount > 0
    
    async def delete_by_path(self, file_path: str) -> bool:
        """根据文件路径删除音频文件记录"""
        result = await self.session.execute(
            delete(AudioFile)
            .where(AudioFile.file_path == file_path)
        )
        await self.session.commit()
        return result.rowcount > 0
    
    async def delete_by_member(self, member_id: int) -> int:
        """删除指定成员的所有音频文件记录"""
        result = await self.session.execute(
            delete(AudioFile)
            .where(AudioFile.member_id == member_id)
        )
        await self.session.commit()
        return result.rowcount
    
    async def get_file_count(
        self,
        member_id: Optional[int] = None,
        file_type: Optional[str] = None
    ) -> int:
        """获取音频文件数量"""
        query = select(AudioFile)
        
        if member_id:
            query = query.where(AudioFile.member_id == member_id)
        
        if file_type:
            query = query.where(AudioFile.file_type == file_type)
        
        result = await self.session.execute(query)
        return len(result.scalars().all())
    
    async def get_total_size(
        self,
        member_id: Optional[int] = None
    ) -> int:
        """获取音频文件总大小（字节）"""
        query = select(AudioFile.file_size)
        
        if member_id:
            query = query.where(AudioFile.member_id == member_id)
        
        result = await self.session.execute(query)
        sizes = result.scalars().all()
        return sum(size for size in sizes if size is not None)