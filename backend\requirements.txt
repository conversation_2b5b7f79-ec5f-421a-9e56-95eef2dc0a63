# Web框架
fastapi==0.104.1
uvicorn[standard]==0.24.0

# 数据库
sqlalchemy==2.0.23
aiosqlite==0.19.0
alembic==1.13.1

# 数据验证和序列化
pydantic==2.5.0
pydantic-settings==2.1.0

# HTTP客户端
httpx==0.25.2
requests==2.31.0

# 异步文件操作
aiofiles==23.2.1

# 音频处理
librosa==0.10.1
soundfile==0.12.1
numpy==1.24.3
scipy==1.11.4

# 语音识别 - Sherpa-ncnn
sherpa-ncnn==1.9.25

# 声纹识别 - WeSpeaker
onnxruntime==1.16.3
torch==2.1.0
torchaudio==2.1.0
sklearn==1.3.2

# JSON处理和查询
jsonschema==4.20.0

# 系统监控
psutil==5.9.6

# 日志
loguru==0.7.2

# 开发工具
pytest==7.4.3
pytest-asyncio==0.21.1
black==23.11.0
flake8==6.1.0
isort==5.12.0

# 阿里百炼平台SDK
dashscope==1.17.0
alibabacloud-nls-py-sdk==1.0.0

# LLM和AI相关
openai==1.3.0  # 兼容接口
tiktoken==0.5.1  # token计算

# 其他工具
python-multipart==0.0.6
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-dotenv==1.0.0

# 音频录制和处理（可选）
PyAudio==0.2.11
pydub==0.25.1