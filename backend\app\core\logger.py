"""
日志配置系统
"""

import sys
import os
from pathlib import Path
from loguru import logger
from app.core.config import settings


def setup_logger():
    """配置日志系统"""
    
    # 移除默认的日志处理器
    logger.remove()
    
    # 确保日志目录存在
    log_dir = Path(settings.LOGS_DIR)
    log_dir.mkdir(exist_ok=True)
    
    # 日志格式
    log_format = (
        "<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
        "<level>{level: <8}</level> | "
        "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
        "<level>{message}</level>"
    )
    
    # 控制台日志（开发模式）
    if settings.DEBUG:
        logger.add(
            sys.stdout,
            format=log_format,
            level="DEBUG",
            colorize=True,
            backtrace=True,
            diagnose=True
        )
    else:
        logger.add(
            sys.stdout,
            format=log_format,
            level="INFO",
            colorize=True
        )
    
    # 文件日志 - 所有日志
    logger.add(
        log_dir / "j_family00.log",
        format=log_format,
        level="DEBUG",
        rotation="10 MB",
        retention="30 days",
        compression="zip",
        encoding="utf-8",
        backtrace=True,
        diagnose=True
    )
    
    # 文件日志 - 错误日志
    logger.add(
        log_dir / "errors.log",
        format=log_format,
        level="ERROR",
        rotation="5 MB",
        retention="60 days",
        compression="zip",
        encoding="utf-8",
        backtrace=True,
        diagnose=True
    )
    
    # 文件日志 - API访问日志
    logger.add(
        log_dir / "api_access.log",
        format=(
            "<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
            "<level>{level: <8}</level> | "
            "{message}"
        ),
        level="INFO",
        rotation="20 MB",
        retention="90 days",
        compression="zip",
        encoding="utf-8",
        filter=lambda record: "api_access" in record["extra"]
    )
    
    # 文件日志 - 语音识别日志
    logger.add(
        log_dir / "speech_recognition.log",
        format=log_format,
        level="INFO",
        rotation="10 MB",
        retention="30 days",
        compression="zip",
        encoding="utf-8",
        filter=lambda record: "speech" in record["extra"]
    )
    
    # 文件日志 - 声纹识别日志
    logger.add(
        log_dir / "voiceprint.log",
        format=log_format,
        level="INFO",
        rotation="10 MB",
        retention="30 days",
        compression="zip",
        encoding="utf-8",
        filter=lambda record: "voiceprint" in record["extra"]
    )
    
    # 文件日志 - 记忆管理日志
    logger.add(
        log_dir / "memory.log",
        format=log_format,
        level="INFO",
        rotation="10 MB",
        retention="30 days",
        compression="zip",
        encoding="utf-8",
        filter=lambda record: "memory" in record["extra"]
    )
    
    logger.info("日志系统初始化完成")


def get_logger(name: str = None):
    """获取日志器实例"""
    if name:
        return logger.bind(name=name)
    return logger


# 创建特定模块的日志器
def get_api_logger():
    """获取API日志器"""
    return logger.bind(api_access=True)


def get_speech_logger():
    """获取语音识别日志器"""
    return logger.bind(speech=True)


def get_voiceprint_logger():
    """获取声纹识别日志器"""
    return logger.bind(voiceprint=True)


def get_memory_logger():
    """获取记忆管理日志器"""
    return logger.bind(memory=True)


# 初始化日志系统
setup_logger()

# 导出主要的日志器
__all__ = [
    "logger",
    "get_logger",
    "get_api_logger", 
    "get_speech_logger",
    "get_voiceprint_logger",
    "get_memory_logger"
]
