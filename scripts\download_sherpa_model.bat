@echo off
echo ==================================
echo   Sherpa-ncnn 中文模型下载工具
echo ==================================
echo.

REM 创建模型目录
set MODELS_DIR=.\models
if not exist "%MODELS_DIR%" mkdir "%MODELS_DIR%"

echo 推荐的中文语音识别模型：
echo 1. sherpa-ncnn-streaming-zipformer-zh-14M-2023-02-23 ^(轻量级，14M^)
echo 2. sherpa-ncnn-streaming-zipformer-bilingual-zh-en-2023-02-13 ^(中英双语^)
echo 3. sherpa-ncnn-streaming-zipformer-zh-20M-2023-02-17 ^(中等大小，20M^)
echo.

set /p choice="请选择要下载的模型 (1-3): "

if "%choice%"=="1" (
    set MODEL_NAME=sherpa-ncnn-streaming-zipformer-zh-14M-2023-02-23
) else if "%choice%"=="2" (
    set MODEL_NAME=sherpa-ncnn-streaming-zipformer-bilingual-zh-en-2023-02-13
) else if "%choice%"=="3" (
    set MODEL_NAME=sherpa-ncnn-streaming-zipformer-zh-20M-2023-02-17
) else (
    echo 无效选择，退出
    pause
    exit /b 1
)

set MODEL_URL=https://github.com/k2-fsa/sherpa-ncnn/releases/download/models/%MODEL_NAME%.tar.bz2

echo.
echo 开始下载模型: %MODEL_NAME%
echo 下载地址: %MODEL_URL%
echo.

REM 检查是否安装了curl
curl --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到 curl，请手动下载模型
    echo 下载地址: %MODEL_URL%
    echo 解压到: %MODELS_DIR%\sherpa-ncnn-zh\
    echo.
    echo 或者安装 curl: https://curl.se/windows/
    pause
    exit /b 1
)

REM 下载模型
cd "%MODELS_DIR%"
echo 正在下载...
curl -L -o "%MODEL_NAME%.tar.bz2" "%MODEL_URL%"

if %errorlevel% equ 0 (
    echo 下载完成！
    echo.
    echo ==================================
    echo   下载完成！
    echo ==================================
    echo 压缩包保存在: %MODELS_DIR%\%MODEL_NAME%.tar.bz2
    echo.
    echo 请使用以下工具解压文件:
    echo - 7-Zip: https://www.7-zip.org/
    echo - WinRAR: https://www.win-rar.com/
    echo.
    echo 解压后请将文件夹重命名为: sherpa-ncnn-zh
    echo 最终路径应为: %MODELS_DIR%\sherpa-ncnn-zh\
    echo.
    echo 解压完成后即可启动 J_family00 语音识别服务！
) else (
    echo 下载失败，请检查网络连接或手动下载
    echo 手动下载地址: %MODEL_URL%
)

echo.
pause