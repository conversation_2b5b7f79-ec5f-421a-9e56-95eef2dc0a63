"""
对话管理端点
"""

from fastapi import APIRouter, HTTPException, status, Query
from typing import List, Optional, Dict, Any
from app.models.schemas import (
    ConversationCreate,
    ConversationResponse
)
from app.services.conversation_service import ConversationService
from app.services.tts_service import TTSService
from app.core.logger import get_logger

logger = get_logger("api")

router = APIRouter()


@router.post("/", response_model=ConversationResponse)
async def create_conversation(
    conversation_data: ConversationCreate
):
    """创建新的对话记录"""
    try:
        conversation_service = ConversationService()
        conversation = await conversation_service.create_conversation(conversation_data)
        return conversation
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"创建对话记录失败: {str(e)}"
        )


@router.get("/", response_model=List[ConversationResponse])
async def get_conversations(
    member_id: Optional[int] = None,
    skip: int = 0,
    limit: int = 50
):
    """获取对话记录列表"""
    try:
        conversation_service = ConversationService()
        conversations = await conversation_service.get_conversations(
            member_id=member_id, 
            skip=skip, 
            limit=limit
        )
        return conversations
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取对话记录失败: {str(e)}"
        )


@router.get("/{conversation_id}", response_model=ConversationResponse)
async def get_conversation(
    conversation_id: int
):
    """根据ID获取对话记录"""
    conversation_service = ConversationService()
    conversation = await conversation_service.get_conversation(conversation_id)
    if not conversation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="对话记录不存在"
        )
    return conversation


@router.delete("/{conversation_id}")
async def delete_conversation(
    conversation_id: int
):
    """删除对话记录"""
    try:
        conversation_service = ConversationService()
        success = await conversation_service.delete_conversation(conversation_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="对话记录不存在"
            )
        return {"message": "对话记录删除成功"}
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"删除对话记录失败: {str(e)}"
        )


@router.post("/chat", response_model=Dict[str, Any])
async def chat_with_ai(
    user_input: str = Query(..., description="用户输入的文本"),
    member_id: Optional[int] = Query(None, description="家庭成员ID"),
    member_name: Optional[str] = Query(None, description="家庭成员姓名"),
    session_id: Optional[str] = Query(None, description="会话ID"),
    enable_tts: bool = Query(False, description="是否启用语音合成"),
    voice: Optional[str] = Query(None, description="TTS语音类型")
):
    """与AI家人智能对话"""
    try:
        conversation_service = ConversationService()

        # 生成AI回复
        result = await conversation_service.generate_ai_response(
            user_input=user_input,
            member_id=member_id,
            member_name=member_name,
            session_id=session_id
        )

        # 如果启用TTS，生成语音
        if enable_tts and result.get("success") and result.get("response"):
            try:
                tts_service = TTSService()
                tts_result = await tts_service.text_to_speech(
                    text=result["response"],
                    voice=voice
                )

                # 添加音频信息到结果
                result["audio"] = {
                    "audio_path": tts_result["audio_path"],
                    "audio_url": tts_service.get_audio_url(tts_result["audio_path"]),
                    "filename": tts_result["filename"],
                    "duration": tts_result["duration"],
                    "voice": tts_result["voice"]
                }

                logger.info(f"AI对话+TTS完成: {member_name}, 音频: {tts_result['filename']}")

            except Exception as tts_error:
                logger.warning(f"TTS生成失败: {str(tts_error)}")
                result["audio_error"] = str(tts_error)

        return result

    except Exception as e:
        logger.error(f"AI对话失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"AI对话失败: {str(e)}"
        )


@router.post("/tts", response_model=Dict[str, Any])
async def text_to_speech(
    text: str = Query(..., description="要合成的文本"),
    voice: Optional[str] = Query(None, description="语音类型"),
    speed: Optional[float] = Query(None, description="语速 (0.5-2.0)"),
    pitch: Optional[float] = Query(None, description="音调 (-500-500)"),
    volume: Optional[float] = Query(None, description="音量 (0-100)")
):
    """文本转语音"""
    try:
        tts_service = TTSService()

        result = await tts_service.text_to_speech(
            text=text,
            voice=voice,
            speed=speed,
            pitch=pitch,
            volume=volume
        )

        # 添加访问URL
        result["audio_url"] = tts_service.get_audio_url(result["audio_path"])

        logger.info(f"TTS合成完成: {result['filename']}")
        return result

    except Exception as e:
        logger.error(f"TTS合成失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"TTS合成失败: {str(e)}"
        )


@router.get("/tts/voices", response_model=Dict[str, Any])
async def get_available_voices():
    """获取可用的TTS语音列表"""
    try:
        tts_service = TTSService()
        voices = await tts_service.get_available_voices()
        return voices
    except Exception as e:
        logger.error(f"获取语音列表失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取语音列表失败: {str(e)}"
        )