"""
应用配置管理系统
"""

import os
import json
from pathlib import Path
from typing import Dict, Any, Optional
from app.core.config import settings


class ConfigManager:
    """配置管理器"""
    
    def __init__(self):
        self.config_dir = Path(settings.DATA_DIR) / "config"
        self.config_dir.mkdir(parents=True, exist_ok=True)
        self.config_file = self.config_dir / "app_config.json"
        self._config_cache = {}
        self._load_config()
    
    def _load_config(self):
        """加载配置文件"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self._config_cache = json.load(f)
            else:
                self._config_cache = self._get_default_config()
                self._save_config()
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            self._config_cache = self._get_default_config()
    
    def _save_config(self):
        """保存配置文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self._config_cache, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存配置文件失败: {e}")
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "app": {
                "name": settings.PROJECT_NAME,
                "version": settings.VERSION,
                "debug": settings.DEBUG,
                "language": "zh-CN",
                "theme": "default"
            },
            "audio": {
                "max_file_size": 50 * 1024 * 1024,  # 50MB
                "supported_formats": ["wav", "mp3", "flac", "aac"],
                "sample_rate": 16000,
                "channels": 1,
                "auto_noise_reduction": True
            },
            "speech_recognition": {
                "engine": "sherpa-ncnn",
                "model_path": "./models/sherpa-ncnn-zh",
                "language": "zh",
                "confidence_threshold": 0.7,
                "timeout": 30,
                "auto_detect_language": False
            },
            "voice_recognition": {
                "engine": "wespeaker",
                "model_path": "./models/wespeaker/voxceleb_resnet34.onnx",
                "similarity_threshold": settings.VOICE_SIMILARITY_THRESHOLD,
                "enrollment_samples": 3,
                "min_audio_length": 2.0,
                "max_audio_length": 30.0
            },
            "memory": {
                "max_entries_per_member": settings.MEMORY_MAX_SIZE,
                "backup_interval_hours": settings.MEMORY_BACKUP_INTERVAL,
                "auto_cleanup_days": 90,
                "search_cache_size": 1000
            },
            "security": {
                "max_login_attempts": 5,
                "session_timeout": 30,
                "require_voice_auth": False,
                "data_encryption": True
            },
            "ui": {
                "max_conversation_history": 100,
                "auto_save_interval": 5,
                "show_confidence_scores": True,
                "enable_animations": True
            }
        }
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值，支持点符号路径"""
        keys = key.split('.')
        value = self._config_cache
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any):
        """设置配置值，支持点符号路径"""
        keys = key.split('.')
        config = self._config_cache
        
        # 导航到最后一级
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        # 设置值
        config[keys[-1]] = value
        self._save_config()
    
    def update(self, updates: Dict[str, Any]):
        """批量更新配置"""
        for key, value in updates.items():
            self.set(key, value)
    
    def reset_to_default(self, section: Optional[str] = None):
        """重置配置为默认值"""
        if section:
            default_config = self._get_default_config()
            if section in default_config:
                self._config_cache[section] = default_config[section]
        else:
            self._config_cache = self._get_default_config()
        
        self._save_config()
    
    def get_all(self) -> Dict[str, Any]:
        """获取所有配置"""
        return self._config_cache.copy()
    
    def export_config(self, file_path: str):
        """导出配置到文件"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self._config_cache, f, ensure_ascii=False, indent=2)
        except Exception as e:
            raise Exception(f"导出配置失败: {e}")
    
    def import_config(self, file_path: str):
        """从文件导入配置"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                imported_config = json.load(f)
            
            # 验证配置格式
            if not isinstance(imported_config, dict):
                raise ValueError("配置文件格式无效")
            
            self._config_cache = imported_config
            self._save_config()
        except Exception as e:
            raise Exception(f"导入配置失败: {e}")
    
    def validate_config(self) -> Dict[str, Any]:
        """验证配置有效性"""
        issues = []
        
        # 验证音频配置
        audio_config = self.get('audio', {})
        if audio_config.get('max_file_size', 0) <= 0:
            issues.append("音频最大文件大小必须大于0")
        
        # 验证语音识别配置
        sr_config = self.get('speech_recognition', {})
        threshold = sr_config.get('confidence_threshold', 0)
        if not 0 <= threshold <= 1:
            issues.append("语音识别置信度阈值必须在0-1之间")
        
        # 验证声纹识别配置
        vr_config = self.get('voice_recognition', {})
        similarity = vr_config.get('similarity_threshold', 0)
        if not 0 <= similarity <= 1:
            issues.append("声纹相似度阈值必须在0-1之间")
        
        # 验证记忆配置
        memory_config = self.get('memory', {})
        max_entries = memory_config.get('max_entries_per_member', 0)
        if max_entries <= 0:
            issues.append("每个成员最大记忆条目数必须大于0")
        
        return {
            "valid": len(issues) == 0,
            "issues": issues
        }


# 全局配置管理器实例
config_manager = ConfigManager()