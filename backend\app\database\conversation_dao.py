"""
对话数据访问对象 (DAO)
"""

from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, delete, and_
from sqlalchemy.orm import selectinload
from app.models.database import Conversation
from app.models.schemas import ConversationCreate


class ConversationDAO:
    """对话数据访问对象"""
    
    def __init__(self, session: AsyncSession):
        self.session = session
    
    async def create(self, conversation_data: ConversationCreate) -> Conversation:
        """创建对话记录"""
        conversation = Conversation(
            member_id=conversation_data.member_id,
            input_text=conversation_data.input_text,
            input_audio_path=conversation_data.input_audio_path,
            response_text=conversation_data.response_text,
            confidence_score=conversation_data.confidence_score,
            session_id=conversation_data.session_id
        )
        
        self.session.add(conversation)
        await self.session.commit()
        await self.session.refresh(conversation)
        return conversation
    
    async def get_by_id(self, conversation_id: int) -> Optional[Conversation]:
        """根据ID获取对话记录"""
        result = await self.session.execute(
            select(Conversation)
            .where(Conversation.id == conversation_id)
            .options(selectinload(Conversation.member))
        )
        return result.scalar_one_or_none()
    
    async def get_by_member(
        self, 
        member_id: int, 
        skip: int = 0, 
        limit: int = 50
    ) -> List[Conversation]:
        """获取指定成员的对话记录"""
        result = await self.session.execute(
            select(Conversation)
            .where(Conversation.member_id == member_id)
            .offset(skip)
            .limit(limit)
            .order_by(Conversation.created_at.desc())
        )
        return result.scalars().all()
    
    async def get_by_session(self, session_id: str) -> List[Conversation]:
        """获取指定会话的对话记录"""
        result = await self.session.execute(
            select(Conversation)
            .where(Conversation.session_id == session_id)
            .order_by(Conversation.created_at.asc())
        )
        return result.scalars().all()
    
    async def get_all(
        self, 
        member_id: Optional[int] = None,
        skip: int = 0, 
        limit: int = 50
    ) -> List[Conversation]:
        """获取所有对话记录"""
        query = select(Conversation)
        
        if member_id:
            query = query.where(Conversation.member_id == member_id)
        
        query = query.offset(skip).limit(limit).order_by(Conversation.created_at.desc())
        
        result = await self.session.execute(query)
        return result.scalars().all()
    
    async def delete(self, conversation_id: int) -> bool:
        """删除对话记录"""
        result = await self.session.execute(
            delete(Conversation)
            .where(Conversation.id == conversation_id)
        )
        await self.session.commit()
        return result.rowcount > 0
    
    async def delete_by_member(self, member_id: int) -> int:
        """删除指定成员的所有对话记录"""
        result = await self.session.execute(
            delete(Conversation)
            .where(Conversation.member_id == member_id)
        )
        await self.session.commit()
        return result.rowcount
    
    async def delete_by_session(self, session_id: str) -> int:
        """删除指定会话的所有对话记录"""
        result = await self.session.execute(
            delete(Conversation)
            .where(Conversation.session_id == session_id)
        )
        await self.session.commit()
        return result.rowcount
    
    async def update_response(
        self, 
        conversation_id: int, 
        response_text: str, 
        response_audio_path: Optional[str] = None
    ) -> Optional[Conversation]:
        """更新对话回复"""
        conversation = await self.get_by_id(conversation_id)
        if conversation:
            conversation.response_text = response_text
            if response_audio_path:
                conversation.response_audio_path = response_audio_path
            
            await self.session.commit()
            await self.session.refresh(conversation)
        
        return conversation
    
    async def get_recent_conversations(
        self, 
        member_id: int, 
        limit: int = 10
    ) -> List[Conversation]:
        """获取指定成员最近的对话记录"""
        result = await self.session.execute(
            select(Conversation)
            .where(Conversation.member_id == member_id)
            .order_by(Conversation.created_at.desc())
            .limit(limit)
        )
        return result.scalars().all()
    
    async def get_conversation_count(self, member_id: Optional[int] = None) -> int:
        """获取对话记录数量"""
        if member_id:
            result = await self.session.execute(
                select(Conversation)
                .where(Conversation.member_id == member_id)
            )
        else:
            result = await self.session.execute(select(Conversation))
        
        return len(result.scalars().all())