# J_family00 智能家庭助手配置文件模板
# 复制此文件为 .env 并根据需要修改配置

# 基础配置
PROJECT_NAME=J_family00
VERSION=0.1.0
DEBUG=true

# 服务器配置
HOST=127.0.0.1
PORT=8000
API_V1_STR=/api/v1

# 跨域配置（多个地址用逗号分隔）
ALLOWED_HOSTS=http://localhost:3000,http://127.0.0.1:3000

# 数据库配置
DATABASE_URL=sqlite:///./data/j_family00.db

# 文件存储路径
DATA_DIR=./data
MEMORIES_DIR=./data/memories
AUDIO_DIR=./data/audio
VOICES_DIR=./data/voices
MODELS_DIR=./models
LOGS_DIR=./logs

# 语音识别配置
SPEECH_RECOGNITION_MODEL=sherpa-ncnn
SHERPA_NCNN_MODEL_PATH=./models/sherpa-ncnn-zh
VOSK_MODEL_PATH=./models/vosk-model-cn

# 声纹识别配置
VOICE_SIMILARITY_THRESHOLD=0.7
VOICE_EMBEDDING_SIZE=256
WESPEAKER_MODEL_PATH=./models/wespeaker/voxceleb_resnet34.onnx

# 记忆存储配置
MEMORY_MAX_SIZE=10000
MEMORY_BACKUP_INTERVAL=24

# 安全配置
SECRET_KEY=your-secret-key-here
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=30

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=./logs/j_family00.log

# 模型下载配置
MODEL_AUTO_DOWNLOAD=true
MODEL_CACHE_DIR=./models/cache

# 开发配置
DEV_MODE=true
DEV_CORS_ALLOW_ALL=true
DEV_LOG_SQL=false